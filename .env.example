# 数据库配置
DB_PATH=tasks.db
LOG_DIR=log
MAX_LOGS=100

# Flask配置
FLASK_HOST=127.0.0.1
FLASK_PORT=5001
FLASK_DEBUG=False
SECRET_KEY=your-secret-key-here

# SMTP邮件配置
SMTP_HOST=smtp.example.com
SMTP_PORT=465
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-password
SMTP_SENDER=<EMAIL>
SMTP_USE_SSL=True
SMTP_RECIPIENTS=<EMAIL>,<EMAIL>

# 任务执行配置
DEFAULT_MAX_RUNTIME=3600
MAX_CONCURRENT_TASKS=10

# 日志管理配置
LOG_RETENTION_DAYS=30
LOG_COMPRESS_AFTER_DAYS=7
MAX_LOG_SIZE=10485760

# 监控配置
MONITORING_ENABLED=True
MONITORING_INTERVAL=60
HEALTH_CHECK_INTERVAL=300

# 安全配置
RATE_LIMIT_ENABLED=True
MAX_REQUESTS_PER_MINUTE=100
