import smtplib
import requests
import json
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from email.header import Header
from typing import Dict, Any, List, Optional, Union


class Notification:
    """
    通知类，提供邮箱通知和Webhook两种消息提醒方式
    """
    
    def __init__(self, smtp_config: Optional[Dict[str, Any]] = None, log_level=logging.INFO):
        """
        初始化通知类
        
        参数:
            smtp_config: SMTP配置，包含以下字段:
                - host: SMTP服务器地址
                - port: SMTP服务器端口
                - username: SMTP用户名
                - password: SMTP密码
                - sender: 发件人邮箱
                - use_ssl: 是否使用SSL (默认True)
            log_level: 日志级别
        """
        # 配置日志
        self.logger = logging.getLogger("Notification")
        self.logger.setLevel(log_level)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        # 设置SMTP配置
        self.smtp_config = smtp_config or {}
    
    def set_smtp_config(self, smtp_config: Dict[str, Any]) -> None:
        """
        设置SMTP配置
        
        参数:
            smtp_config: SMTP配置，包含以下字段:
                - host: SMTP服务器地址
                - port: SMTP服务器端口
                - username: SMTP用户名
                - password: SMTP密码
                - sender: 发件人邮箱
                - use_ssl: 是否使用SSL (默认True)
        """
        self.smtp_config = smtp_config
    
    def send_email(self, 
                  subject: str, 
                  content: str, 
                  recipients: List[str], 
                  smtp_config: Optional[Dict[str, Any]] = None,
                  html_content: Optional[str] = None) -> bool:
        """
        发送邮件通知
        
        参数:
            subject: 邮件主题
            content: 邮件内容（纯文本）
            recipients: 收件人列表
            smtp_config: 临时SMTP配置，如不提供则使用初始化时设置的配置
            html_content: HTML格式的邮件内容（可选）
            
        返回:
            bool: 是否发送成功
        """
        try:
            # 使用传入的配置或默认配置
            config = smtp_config or self.smtp_config
            
            # 提取SMTP配置
            host = config.get('host')
            port = config.get('port', 465)
            username = config.get('username')
            password = config.get('password')
            sender = config.get('sender', username)
            use_ssl = config.get('use_ssl', True)
            
            if not all([host, username, password, sender]):
                self.logger.error("SMTP配置不完整")
                return False
            
            # 创建邮件
            msg = MIMEMultipart('alternative')
            msg['Subject'] = Header(subject, 'utf-8')
            msg['From'] = sender
            msg['To'] = ", ".join(recipients)
            
            # 添加纯文本内容
            text_part = MIMEText(content, 'plain', 'utf-8')
            msg.attach(text_part)
            
            # 如果提供了HTML内容，也添加HTML部分
            if html_content:
                html_part = MIMEText(html_content, 'html', 'utf-8')
                msg.attach(html_part)
            
            # 连接SMTP服务器并发送
            if use_ssl:
                server = smtplib.SMTP_SSL(host, port)
            else:
                server = smtplib.SMTP(host, port)
                server.starttls()
            
            server.login(username, password)
            server.sendmail(sender, recipients, msg.as_string())
            server.quit()
            
            self.logger.info(f"邮件已发送至 {len(recipients)} 个收件人")
            return True
            
        except Exception as e:
            self.logger.error(f"发送邮件失败: {str(e)}")
            return False
    
    def send_webhook(self, 
                    webhook_url: str, 
                    payload: Dict[str, Any], 
                    headers: Optional[Dict[str, str]] = None,
                    method: str = 'POST',
                    timeout: int = 10) -> bool:
        """
        发送Webhook通知
        
        参数:
            webhook_url: Webhook URL
            payload: 要发送的数据
            headers: 请求头（可选）
            method: 请求方法（默认为POST）
            timeout: 请求超时时间（秒）
            
        返回:
            bool: 是否发送成功
        """
        try:
            # 设置默认请求头
            if headers is None:
                headers = {'Content-Type': 'application/json'}
            
            # 发送请求
            if method.upper() == 'GET':
                response = requests.get(
                    webhook_url, 
                    params=payload, 
                    headers=headers, 
                    timeout=timeout
                )
            else:  # 默认使用POST
                response = requests.post(
                    webhook_url, 
                    data=json.dumps(payload), 
                    headers=headers, 
                    timeout=timeout
                )
            
            # 检查响应
            response.raise_for_status()
            self.logger.info(f"Webhook通知已发送: {webhook_url}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送Webhook通知失败: {str(e)}")
            return False
    
    def send_task_notification(self, 
                              task_info: Dict[str, Any], 
                              notification_type: str, 
                              notification_config: Optional[Dict[str, Any]] = None) -> bool:
        """
        发送任务相关通知
        
        参数:
            task_info: 任务信息
            notification_type: 通知类型 ('email' 或 'webhook')
            notification_config: 通知配置，如不提供则使用默认配置
            
        返回:
            bool: 是否发送成功
        """
        try:
            # 提取任务信息
            task_id = task_info.get('id', 'unknown')
            task_name = task_info.get('name', f'Task {task_id}')
            status = task_info.get('status', 'unknown')
            start_time = task_info.get('start_time', 'unknown')
            end_time = task_info.get('end_time', 'unknown')
            duration = task_info.get('duration', 'unknown')
            
            # 根据通知类型发送不同的通知
            if notification_type.lower() == 'email':
                # 准备邮件内容
                subject = f"任务通知: {task_name} ({status})"
                content = f"""
任务ID: {task_id}
任务名称: {task_name}
状态: {status}
开始时间: {start_time}
结束时间: {end_time}
持续时间: {duration} 秒
                """
                # 使用提供的配置或默认配置
                config = notification_config or {}
                recipients = config.get('recipients', [])
                if not recipients:
                    print(f"没有配置收件人，跳过邮件发送")
                    return False
                # del config['recipients']
                self.smtp_config.update(config)
                # 发送邮件
                # print(subject,content,recipients,self.smtp_config)
                return self.send_email(
                    subject=subject,
                    content=content,
                    recipients=recipients,
                    smtp_config=self.smtp_config
                )
                
            elif notification_type.lower() == 'webhook':
                # 准备Webhook负载
                payload = {
                    'task_id': task_id,
                    'task_name': task_name,
                    'status': status,
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': duration
                }
                
                # 使用提供的配置或默认配置
                config = notification_config or {}
                
                # 发送Webhook
                return self.send_webhook(
                    webhook_url=config.get('webhook_url', ''),
                    payload=payload,
                    headers=config.get('headers', None),
                    method=config.get('method', 'POST')
                )
                
            else:
                self.logger.error(f"不支持的通知类型: {notification_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"发送任务通知失败: {str(e)}")
            return False 