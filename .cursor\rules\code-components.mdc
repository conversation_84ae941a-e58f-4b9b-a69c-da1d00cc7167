---
description: 任务管理系统 - 代码组件
globs: 
alwaysApply: false
---
# 任务管理系统 - 代码组件

## Task_manager 类

[task_manager.py](mdc:task_manager.py) 中的核心类，负责任务管理的全部功能。

主要方法:
- `add_task()` - 添加新的定时任务，设置脚本路径、执行计划和运行参数
- `edit_task()` - 修改现有任务的配置
- `run_task_now()` - 立即手动运行指定任务
- `terminate_task()` - 终止正在运行的任务
- `remove_task()` - 移除任务
- `get_tasks()` - 获取所有任务列表
- `get_task_logs()` - 获取任务执行日志
- `_scheduler_loop()` - 任务调度循环，定期检查和执行到期的任务
- `_execute_task()` - 执行单个任务的具体实现
- `_send_task_notifications()` - 发送任务执行结果通知

## Notification 类

[notification.py](mdc:notification.py) 中的通知类，处理任务执行结果的通知。

主要方法:
- `send_email()` - 发送邮件通知
- `send_webhook()` - 发送Webhook通知
- `send_task_notification()` - 根据任务执行结果发送相应通知

## Web应用接口

[app.py](mdc:app.py) 提供Web界面和API接口。

主要路由:
- `/` - 显示管理界面
- `/api/tasks` - 任务管理API（获取、添加、修改、删除任务）
- `/api/tasks/<task_id>/run` - 手动运行任务API
- `/api/tasks/<task_id>/terminate` - 终止任务API
- `/api/tasks/<task_id>/logs` - 获取任务日志API
- `/api/dashboard` - 获取仪表盘概览数据API

## 数据库结构

使用SQLite数据库 [tasks.db](mdc:tasks.db) 存储数据，主要表结构:

1. `tasks` - 存储任务配置
   - id, name, script_path, working_dir, max_runtime, retry_on_error, cron_expression, remark, is_active

2. `task_runs` - 存储任务执行记录
   - id, task_id, start_time, end_time, duration, status, log_file

3. `notification_config` - 存储任务通知配置
   - id, task_id, notification_type, config, is_active, notify_on_error

