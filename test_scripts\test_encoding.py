# -*- coding: utf-8 -*-
"""
编码和字符处理测试脚本
专门用于测试Windows环境下的UTF-8编码和\r字符处理
"""
import sys
import datetime

def test_encoding():
    """测试各种编码字符"""
    print("=== 编码测试开始 ===")
    print(f"Python版本: {sys.version}")
    print(f"默认编码: {sys.getdefaultencoding()}")
    print(f"文件系统编码: {sys.getfilesystemencoding()}")
    
    # 测试中文
    print("中文测试: 你好世界！")
    
    # 测试Unicode表情符号
    print("表情符号测试:")
    print("❤️ 爱心")
    print("🎉 庆祝")
    print("🚀 火箭")
    print("⭐ 星星")
    print("🌍 地球")
    print("🐍 Python")
    
    # 测试特殊Unicode字符
    print("特殊字符测试:")
    print("©️ 版权符号")
    print("®️ 注册商标")
    print("™️ 商标")
    print("€ 欧元符号")
    print("£ 英镑符号")
    print("¥ 人民币符号")
    
    # 测试数学符号
    print("数学符号测试:")
    print("∑ 求和")
    print("∞ 无穷大")
    print("π 圆周率")
    print("√ 平方根")
    print("≈ 约等于")
    
    print("=== 编码测试结束 ===\n")

def test_carriage_return():
    """测试回车符处理"""
    print("=== \\r字符测试开始 ===")
    
    # 基本\r测试
    print("基本\\r测试:")
    print("123\r456")  # 应该显示为两行：123 和 456
    
    # 多个\r测试
    print("多个\\r测试:")
    print("aaa\rbbb\rccc")  # 应该显示为三行
    
    # \r与其他字符混合
    print("\\r与Unicode混合:")
    print("Hello\r世界❤️")
    print("🚀\rPython\r🐍")
    
    # \r与\n混合
    print("\\r与\\n混合:")
    print("Line1\rLine2\nLine3\rLine4")
    
    # 连续\r测试
    print("连续\\r测试:")
    print("A\r\rB\r\r\rC")
    
    print("=== \\r字符测试结束 ===\n")

def test_mixed_content():
    """测试混合内容"""
    print("=== 混合内容测试开始 ===")
    
    # 长文本与特殊字符
    long_text = "这是一个包含各种字符的长文本：" + "中文" * 5 + "❤️" + "English" * 3 + "🌍"
    print(f"长文本: {long_text}")
    
    # 包含\r的长文本
    mixed_text = "开始\r中间内容包含Unicode❤️\r结束🎉"
    print(f"混合文本: {mixed_text}")
    
    # 数字与特殊字符
    for i in range(3):
        print(f"循环 {i}: 数字{i} ❤️ Unicode\r下一行内容")
    
    print("=== 混合内容测试结束 ===\n")

def test_error_scenarios():
    """测试错误场景"""
    print("=== 错误场景测试开始 ===")
    
    try:
        # 测试可能导致编码错误的字符
        problematic_chars = [
            '\u2764',  # ❤️
            '\u1F389', # 🎉
            '\u1F680', # 🚀
            '\u2B50',  # ⭐
            '\u1F30D', # 🌍
        ]
        
        for char in problematic_chars:
            print(f"测试字符: {char} (Unicode: {repr(char)})")
            print(f"与\\r结合: 前缀\r{char}\r后缀")
    
    except Exception as e:
        print(f"编码错误: {e}")
    
    print("=== 错误场景测试结束 ===\n")

def main():
    """主测试函数"""
    print("=" * 60)
    print("Windows UTF-8 编码和\\r字符处理测试")
    print(f"测试时间: {datetime.datetime.now()}")
    print("=" * 60)
    
    # 运行所有测试
    test_encoding()
    test_carriage_return()
    test_mixed_content()
    test_error_scenarios()
    
    print("=" * 60)
    print("所有测试完成！")
    print("如果您能看到所有Unicode字符和正确的\\r处理，说明修复成功。")
    print("=" * 60)

if __name__ == "__main__":
    main()
