#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试编码修复的脚本
验证\r字符处理和UTF-8编码是否正常工作
"""
import os
import sys
import tempfile
import subprocess
import time
from pathlib import Path


def test_encoding_in_subprocess():
    """测试子进程中的编码处理"""
    print("=" * 60)
    print("测试子进程编码处理")
    print("=" * 60)
    
    # 创建测试脚本
    test_script_content = '''# -*- coding: utf-8 -*-
import sys
print("Python版本:", sys.version)
print("默认编码:", sys.getdefaultencoding())
print("中文测试: 你好世界")
print("Unicode测试: ❤️ 🎉 🚀 ⭐ 🌍")
print("\\r字符测试: 123\\r456")
print("混合测试: Hello\\r世界❤️\\rWorld🌍")
'''
    
    # 写入临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
        f.write(test_script_content)
        temp_script = f.name
    
    try:
        print(f"创建临时测试脚本: {temp_script}")
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUTF8'] = '1'
        
        # 运行测试脚本
        print("运行测试脚本...")
        result = subprocess.run(
            [sys.executable, temp_script],
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            env=env
        )
        
        print("标准输出:")
        print(result.stdout)
        
        if result.stderr:
            print("标准错误:")
            print(result.stderr)
        
        print(f"返回码: {result.returncode}")
        
        # 检查输出是否包含预期内容
        output = result.stdout
        success = True
        
        if "你好世界" not in output:
            print("✗ 中文字符测试失败")
            success = False
        else:
            print("✓ 中文字符测试通过")
        
        if "❤️" not in output:
            print("✗ Unicode字符测试失败")
            success = False
        else:
            print("✓ Unicode字符测试通过")
        
        # 检查\r字符处理
        if "123" in output and "456" in output:
            print("✓ \\r字符处理测试通过")
        else:
            print("✗ \\r字符处理测试失败")
            success = False
        
        return success
        
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_script)
        except:
            pass


def test_task_manager_encoding():
    """测试任务管理器的编码处理"""
    print("\n" + "=" * 60)
    print("测试任务管理器编码处理")
    print("=" * 60)
    
    try:
        # 导入任务管理器相关模块
        sys.path.insert(0, '.')
        from task_manager import Task_manager
        
        print("✓ 任务管理器模块导入成功")
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = os.path.join(temp_dir, 'test.db')
            log_dir = os.path.join(temp_dir, 'logs')
            
            # 禁用监控以简化测试
            os.environ['MONITORING_ENABLED'] = 'False'
            
            # 创建任务管理器
            task_mgr = Task_manager(
                db_path=db_path,
                log_dir=log_dir,
                max_logs=10
            )
            
            print("✓ 任务管理器创建成功")
            
            # 创建测试脚本
            test_script = os.path.join(temp_dir, 'test_encoding.py')
            with open(test_script, 'w', encoding='utf-8') as f:
                f.write('''# -*- coding: utf-8 -*-
print("编码测试开始")
print("中文: 你好世界")
print("Unicode: ❤️ 🎉 🚀")
print("\\r测试: 123\\r456")
print("混合: Hello\\r世界❤️")
print("编码测试结束")
''')
            
            # 添加测试任务
            task_id = task_mgr.add_task(
                script_path=f'python "{test_script}"',
                working_dir=temp_dir,
                cron_expression='*/5 * * * *',
                name='编码测试任务',
                max_runtime=30
            )
            
            print(f"✓ 测试任务已添加，ID: {task_id}")
            
            # 立即运行任务
            success, message = task_mgr.run_task_now(task_id)
            print(f"任务执行结果: {success}, 消息: {message}")
            
            if success:
                # 等待任务完成
                print("等待任务完成...")
                time.sleep(3)
                
                # 获取日志
                logs = task_mgr.get_task_logs(task_id, limit=1)
                if logs:
                    log_content = task_mgr.get_log_content(logs[0]['id'])
                    print("任务日志内容:")
                    print("-" * 40)
                    print(log_content)
                    print("-" * 40)
                    
                    # 检查日志内容
                    if "你好世界" in log_content and "❤️" in log_content:
                        print("✓ 任务管理器编码处理测试通过")
                        return True
                    else:
                        print("✗ 任务管理器编码处理测试失败")
                        return False
                else:
                    print("✗ 未找到任务日志")
                    return False
            else:
                print("✗ 任务执行失败")
                return False
            
    except Exception as e:
        print(f"✗ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理环境变量
        if 'MONITORING_ENABLED' in os.environ:
            del os.environ['MONITORING_ENABLED']


def main():
    """主测试函数"""
    print("Windows UTF-8编码和\\r字符处理修复测试")
    print("=" * 60)
    
    # 显示当前环境信息
    print(f"Python版本: {sys.version}")
    print(f"平台: {sys.platform}")
    print(f"默认编码: {sys.getdefaultencoding()}")
    print(f"PYTHONIOENCODING: {os.environ.get('PYTHONIOENCODING', '未设置')}")
    print(f"PYTHONUTF8: {os.environ.get('PYTHONUTF8', '未设置')}")
    
    tests = [
        ("子进程编码测试", test_encoding_in_subprocess),
        ("任务管理器编码测试", test_task_manager_encoding),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n开始执行: {test_name}")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！编码问题已修复。")
        print("\n使用建议:")
        print("1. 在Windows上使用 start_utf8.bat 启动")
        print("2. 确保脚本文件保存为UTF-8编码")
        print("3. 设置环境变量 PYTHONIOENCODING=utf-8")
    else:
        print("❌ 部分测试失败，需要进一步检查。")
    
    return passed == total


if __name__ == '__main__':
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
