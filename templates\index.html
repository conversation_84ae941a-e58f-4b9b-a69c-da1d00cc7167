<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理系统</title>
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 自定义样式 */
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* 通知样式 */
        .notification {
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 100%;
            transform: translateY(-20px);
            opacity: 0;
            animation: slideIn 0.3s forwards;
        }
        
        @keyframes slideIn {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOut {
            to {
                transform: translateY(-20px);
                opacity: 0;
            }
        }
        
        .notification.success {
            background-color: #ecfdf5;
            border-left: 4px solid #10b981;
            color: #064e3b;
        }
        
        .notification.error {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
            color: #7f1d1d;
        }
        
        .notification.info {
            background-color: #eff6ff;
            border-left: 4px solid #3b82f6;
            color: #1e40af;
        }
        
        .dark .notification.success {
            background-color: #064e3b;
            border-left: 4px solid #10b981;
            color: #ecfdf5;
        }
        
        .dark .notification.error {
            background-color: #7f1d1d;
            border-left: 4px solid #ef4444;
            color: #fef2f2;
        }
        
        .dark .notification.info {
            background-color: #1e40af;
            border-left: 4px solid #3b82f6;
            color: #eff6ff;
        }
        
        .notification-content {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .notification-close {
            cursor: pointer;
            margin-left: 8px;
            opacity: 0.7;
            transition: opacity 0.2s;
        }
        
        .notification-close:hover {
            opacity: 1;
        }
        
        .task-card {
            transition: all 0.2s ease-in-out;
        }
        
        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .btn {
            transition: all 0.2s ease-in-out;
        }
        
        .btn:hover {
            transform: scale(1.03);
        }
        
        /* 状态标签颜色 */
        .status-success { 
            background-color: #ecfdf5; 
            color: #047857; 
            border: 1px solid #a7f3d0;
        }
        .status-error { 
            background-color: #fef2f2; 
            color: #b91c1c; 
            border: 1px solid #fecaca;
        }
        .status-running { 
            background-color: #eff6ff; 
            color: #1e40af; 
            border: 1px solid #bfdbfe;
        }
        .status-timeout { 
            background-color: #fffbeb; 
            color: #92400e; 
            border: 1px solid #fef3c7;
        }
        .status-pending { 
            background-color: #f3f4f6; 
            color: #374151; 
            border: 1px solid #e5e7eb;
        }
        
        /* 表头样式 */
        .table-header {
            background: #4d4c85;
            color: #ffffff;
            font-weight: 600;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }
        
        .dark .table-header {
            background: linear-gradient(to right, #1f2937, #111827);
            color: #d1d5db;
        }
        
        /* z-index自定义 */
        .z-60 {
            z-index: 60;
        }
        
        /* 自定义动画 */
        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        
        .animate-spin {
            animation: spin 1s linear infinite;
        }

        /* 操作列样式 */
        .actions-cell {
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            gap: 8px;
        }

        .action-btn {
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.2s;
            font-size: 1rem;
            min-width: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            background-color: #f3f4f6;
            transform: scale(1.05);
        }

        .dark .action-btn:hover {
            background-color: #374151;
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 顶部通知容器 -->
        <div id="notifications-container" class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 flex flex-col gap-2 max-w-md"></div>
        
        <!-- 顶部标题 -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-2xl font-bold">任务管理系统</h1>
            <div class="flex space-x-2">
                <button id="refresh-btn" class="p-2 rounded-lg bg-gray-200 dark:bg-gray-700" title="刷新数据">
                    <i class="fas fa-sync"></i>
                </button>
                <button id="theme-toggle" class="p-2 rounded-lg bg-gray-200 dark:bg-gray-700" title="切换主题">
                    <i class="fas fa-moon dark:hidden"></i>
                    <i class="fas fa-sun hidden dark:block"></i>
                </button>
                <button id="add-task-btn" class="btn flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-plus"></i>
                    <span>添加任务</span>
                </button>
            </div>
        </div>
        
        <!-- 数据面板 -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">今日数据</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div id="running-panel" class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow task-card cursor-pointer">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-lg font-medium">执行中</h3>
                            <p id="running-count" class="text-3xl font-bold text-blue-600 dark:text-blue-400">-</p>
                        </div>
                        <div class="text-blue-500 dark:text-blue-400 text-4xl">
                            <i class="fas fa-play-circle"></i>
                        </div>
                    </div>
                </div>
                
                <div id="completed-panel" class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow task-card cursor-pointer">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-lg font-medium">已完成</h3>
                            <p id="completed-count" class="text-3xl font-bold text-green-600 dark:text-green-400">-</p>
                        </div>
                        <div class="text-green-500 dark:text-green-400 text-4xl">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
                
                <div id="failed-panel" class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow task-card cursor-pointer">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-lg font-medium">失败</h3>
                            <p id="failed-count" class="text-3xl font-bold text-red-600 dark:text-red-400">-</p>
                        </div>
                        <div class="text-red-500 dark:text-red-400 text-4xl">
                            <i class="fas fa-times-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 面板详情 -->
        <div id="panel-details" class="mb-8 bg-white dark:bg-gray-800 p-6 rounded-lg shadow hidden fade-in">
            <div class="flex justify-between items-center mb-4">
                <h2 id="panel-title" class="text-xl font-semibold"></h2>
                <button id="close-panel" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="panel-content" class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="table-header">
                        <tr>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">任务名称</th>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">开始时间</th>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">耗时</th>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="panel-tasks" class="divide-y divide-gray-200 dark:divide-gray-700 text-center">
                        <!-- 动态加载任务 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 任务列表 -->
        <div>
            <h2 class="text-xl font-semibold mb-4">任务列表</h2>
            
            <!-- 任务过滤 -->
            <div class="mb-4 flex flex-wrap items-center gap-4">
                <div class="relative flex-grow max-w-md">
                    <input type="text" id="task-filter" placeholder="搜索任务..." class="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
                <div class="flex items-center">
                    <input checked type="checkbox" id="show-disabled" class="rounded border-gray-300 dark:border-gray-700 text-blue-600 focus:ring-blue-500 dark:bg-gray-700">
                    <label for="show-disabled" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">显示禁用任务</label>
                </div>
            </div>
            
            <div class="overflow-x-auto bg-white dark:bg-gray-800 rounded-lg shadow">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="table-header">
                        <tr>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">任务名称</th>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">下次执行时间</th>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">上次开始时间</th>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">耗时</th>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider" style="width: 120px;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="tasks-container" class="divide-y divide-gray-200 dark:divide-gray-700 text-center">
                        <!-- 动态加载任务 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 日志弹窗 -->
    <div id="log-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[70] hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-11/12 md:w-3/4 lg:w-2/3 max-h-[80vh] flex flex-col">
            <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 id="log-modal-title" class="text-lg font-semibold">日志详情</h3>
                <button id="close-log-modal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="flex-1 overflow-y-auto p-4">
                <div id="log-content" class="font-mono text-sm whitespace-pre-wrap bg-gray-100 dark:bg-gray-900 p-4 rounded"></div>
            </div>
        </div>
    </div>
    
    <!-- 任务日志列表弹窗 -->
    <div id="log-list-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-11/12 md:w-3/4 lg:w-2/3 max-h-[80vh] flex flex-col">
            <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 id="log-list-modal-title" class="text-lg font-semibold">任务日志列表</h3>
                <button id="close-log-list-modal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="flex-1 overflow-y-auto p-4">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="table-header">
                        <tr>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">开始时间</th>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">结束时间</th>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">耗时</th>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="task-logs" class="divide-y divide-gray-200 dark:divide-gray-700 text-center">
                        <!-- 动态加载日志 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 添加/编辑任务弹窗 -->
    <div id="task-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-11/12 md:w-2/3 lg:w-1/2 max-h-[90vh] flex flex-col">
            <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 id="task-modal-title" class="text-lg font-semibold">添加任务</h3>
                <button id="close-task-modal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="flex-1 overflow-y-auto p-4">
                <form id="task-form" class="space-y-4">
                    <input type="hidden" id="task-id">
                    
                    <div>
                        <label for="task-name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">任务名称</label>
                        <input type="text" id="task-name" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" style="padding: 10px;">
                    </div>
                    
                    <div>
                        <label for="script-path" class="block text-sm font-medium text-gray-700 dark:text-gray-300">脚本路径</label>
                        <input type="text" id="script-path" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" style="padding: 10px;">
                    </div>
                    
                    <div>
                        <label for="working-dir" class="block text-sm font-medium text-gray-700 dark:text-gray-300">工作目录</label>
                        <input type="text" id="working-dir" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" style="padding: 10px;">
                    </div>
                    
                    <div>
                        <label for="cron-expression" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Cron 表达式</label>
                        <input type="text" id="cron-expression" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" placeholder="例如: 0 */4 * * *" style="padding: 10px;">
                    </div>
                    
                    <div>
                        <label for="max-runtime" class="block text-sm font-medium text-gray-700 dark:text-gray-300">最大运行时间（秒）</label>
                        <input type="number" id="max-runtime" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" value="3600" style="padding: 10px;">
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="retry-on-error" class="rounded border-gray-300 dark:border-gray-700 text-blue-600 focus:ring-blue-500 dark:bg-gray-700">
                        <label for="retry-on-error" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">失败时重试</label>
                    </div>
                    
                    <div>
                        <label for="task-remark" class="block text-sm font-medium text-gray-700 dark:text-gray-300">备注</label>
                        <textarea id="task-remark" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" style="padding: 10px;"></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" id="cancel-task" class="btn px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg">取消</button>
                        <button type="submit" class="btn px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 任务操作弹出菜单 -->
    <div id="task-menu" class="hidden absolute bg-white dark:bg-gray-800 rounded-lg shadow-lg py-2 z-40">
        <button id="edit-task" class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">
            <i class="fas fa-edit mr-2"></i> 编辑
        </button>
        <button id="disable-task" class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">
            <i class="fas fa-ban mr-2"></i> 禁用
        </button>
        <button id="delete-task" class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 text-red-600">
            <i class="fas fa-trash mr-2"></i> 删除
        </button>
        <button id="pin-task" class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">
            <i class="fas fa-thumbtack mr-2"></i> 置顶
        </button>
    </div>

    <script>
        // 添加在script代码前，设置键盘事件处理逻辑
        document.addEventListener('DOMContentLoaded', function() {
            // 监听键盘事件
            <!-- document.addEventListener('keydown', function(event) {
                // 当按下ESC键时
                if (event.key === 'Escape') {
                    // 如果两个弹窗都打开，先关闭日志详情弹窗
                    const logModal = document.getElementById('log-modal');
                    const logListModal = document.getElementById('log-list-modal');
                    
                    if (!logModal.classList.contains('hidden')) {
                        logModal.classList.add('hidden');
                        event.preventDefault();
                        event.stopPropagation();
                    } 
                }
            }); -->
            
            // 监听点击关闭按钮关闭日志详情弹窗
            document.getElementById('close-log-modal').addEventListener('click', function() {
                document.getElementById('log-modal').classList.add('hidden');
            });
            
            // 监听点击关闭按钮关闭日志列表弹窗
            document.getElementById('close-log-list-modal').addEventListener('click', function() {
                document.getElementById('log-list-modal').classList.add('hidden');
            });
        });
    </script>
    <script src="/static/js/script.js"></script>
</body>
</html> 