# 任务管理器 (Task Manager)

这是一个用于管理和执行定时任务的Python模块。它允许您添加、编辑、执行和监控Python脚本作为定时任务，并管理其执行日志。

## 功能特点

- 使用crontab格式的时间表达式定时执行任务
- 多任务并行执行
- 任务执行状态监控
- 任务日志管理
- 自动清理过多的日志文件
- 支持任务超时控制
- 支持错误时重试
- 支持编辑现有任务
- 支持手动立即运行任务
- 使用SQLite数据库存储任务信息和运行状态
- 支持任务执行结果通知（邮件和Webhook）

## 新特性: 终止任务

添加了手动终止正在执行的任务功能。当任务正在执行时，界面上会显示终止按钮，点击可以强制停止任务的执行。任务被终止后会标记为"已终止"状态，并记录实际运行时长。

### 主要功能

- 执行中的任务会自动将"执行"按钮切换为"终止"按钮
- 点击终止按钮可以安全结束任务进程
- 终止后的任务会被标记为特殊状态"terminated"
- 终止操作会在日志中记录终止时间和原因

### 使用场景

- 当任务执行时间过长但又不想等待超时时
- 发现任务执行出错需要立即停止时
- 需要释放系统资源时

## TODO

- 修改错误重试机制

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```python
from task_manager import Task_manager

# 创建任务管理器实例
task_mgr = Task_manager(db_path='tasks.db', log_dir='log', max_logs=100)

# 添加定时任务
task_id = task_mgr.add_task(
    script_path='path/to/your/script.py',  # 脚本路径
    working_dir='path/to/working/dir',     # 工作目录
    cron_expression='*/5 * * * *',         # 每5分钟执行一次
    name="测试任务",                        # 任务名称
    max_runtime=3600,                      # 最长运行时间（秒）
    retry_on_error=True,                   # 错误时是否重试
    remark="这是一个测试任务"               # 任务备注
)

# 编辑任务
task_mgr.edit_task(
    task_id=task_id,
    cron_expression='*/10 * * * *',        # 修改为每10分钟执行一次
    max_runtime=1800                       # 修改最长运行时间为1800秒
)

# 手动立即运行任务
success, message = task_mgr.run_task_now(task_id)
print(message)  # 输出: 任务已启动 或 错误信息

# 获取任务日志
logs = task_mgr.get_task_logs(task_id=task_id, limit=10)

# 查看日志内容
log_content = task_mgr.get_log_content(logs[0]['log_file'])

# 停止任务管理器
task_mgr.stop()
```

### 添加通知功能

任务管理器支持两种通知方式：邮件通知和Webhook通知。您可以为任务添加一个或多个通知配置。

#### 初始化时设置SMTP配置

```python
# SMTP配置
smtp_config = {
    'host': 'smtp.example.com',
    'port': 465,
    'username': '<EMAIL>',
    'password': 'your_password',
    'sender': '<EMAIL>',
    'use_ssl': True
}

# 创建任务管理器实例，并在初始化时设置SMTP配置
task_mgr = Task_manager(
    db_path='tasks.db', 
    log_dir='log', 
    max_logs=100,
    smtp_config=smtp_config
)

# 更新SMTP配置
task_mgr.set_smtp_config(smtp_config)
```

#### 添加通知配置

```python
# 添加邮件通知（使用初始化时设置的SMTP配置）
email_config = {
    'recipients': ['<EMAIL>']
}

email_notification_id = task_mgr.add_notification(
    task_id=task_id,
    notification_type='email',
    config=email_config
)

# 添加Webhook通知
webhook_config = {
    'webhook_url': 'https://webhook.example.com/tasks',
    'method': 'POST',
    'headers': {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your_token_here'
    }
}

webhook_notification_id = task_mgr.add_notification(
    task_id=task_id,
    notification_type='webhook',
    config=webhook_config
)

# 获取任务的通知配置
notifications = task_mgr.get_notifications(task_id=task_id)

# 移除通知配置
task_mgr.remove_notification(email_notification_id)
```

### Crontab 表达式格式

任务管理器使用标准的crontab表达式来设置定时任务的执行时间。表达式格式为：

```
分 时 日 月 周
```

例如：
- `* * * * *` - 每分钟执行一次
- `*/5 * * * *` - 每5分钟执行一次
- `0 * * * *` - 每小时整点执行
- `0 9 * * 1-5` - 每个工作日（周一至周五）上午9点执行

## API 参考

### Task_manager 类

**初始化参数**:
- `db_path` - SQLite数据库路径，默认为'tasks.db'
- `log_dir` - 日志存储目录，默认为'log'
- `max_logs` - 最大日志文件数量，默认为100
- `smtp_config` - SMTP配置，用于邮件通知

**主要方法**:
- 添加新任务 `add_task(script_path, working_dir, cron_expression, name="", max_runtime=3600, retry_on_error=False, remark="")`
- 编辑现有任务 `edit_task(task_id, script_path=None, working_dir=None, cron_expression=None, name=None, max_runtime=None, retry_on_error=None, remark=None, is_active=None)`
- 立即手动运行任务 `run_task_now(task_id)`
- 移除任务（设为非活动）  `remove_task(task_id)`
- 获取所有任务 `get_tasks(include_inactive=False)`
- 获取任务执行日志 `get_task_logs(task_id=None, limit=10)`
- 获取日志文件内容 `get_log_content(log_file)`
- 添加通知配置 `add_notification(task_id, notification_type, config)`
- 移除通知配置 `remove_notification(config_id)`
- 获取通知配置 `get_notifications(task_id=None)`
- 设置SMTP配置 `set_smtp_config(smtp_config)`
- 停止任务管理器 `stop()`

### Notification 类

**初始化参数**:
- `smtp_config` - SMTP配置，用于邮件通知
- `log_level` - 日志级别，默认为 logging.INFO

**主要方法**:
- 发送邮件通知 `send_email(subject, content, recipients, smtp_config=None, html_content=None)`
- 发送Webhook通知 `send_webhook(webhook_url, payload, headers=None, method='POST', timeout=10)`
- 发送任务通知 `send_task_notification(task_info, notification_type, notification_config=None)`
- 设置SMTP配置 `set_smtp_config(smtp_config)`

## 运行示例

运行基本示例脚本:

```bash
python example.py
```

运行通知功能示例脚本:

```bash
python notification_example.py
python notification_example_advanced.py
```

## 注意事项

- 任务管理器在后台使用线程来调度和执行任务
- 任务超时后会被自动终止
- 日志文件数量超过设定值后会自动清理最旧的日志
- 编辑任务的cron表达式后，调度器会自动更新任务的执行计划
- 手动运行任务不会影响定时调度计划
- 使用邮件通知功能需要配置正确的SMTP服务器信息
- 使用Webhook通知功能需要确保目标服务器能够接收和处理JSON格式的请求 