# 任务管理器 (Task Manager) v2.0

这是一个高性能、企业级的定时任务管理系统，采用现代化架构设计，提供完整的任务调度、监控和管理功能。

## 🚀 核心特性

### 任务调度
- 使用crontab格式的时间表达式定时执行任务
- 高效的堆队列调度算法，支持大规模任务调度
- 智能的并发控制，可配置最大并发任务数
- 支持任务优先级和队列管理
- 任务执行状态实时监控

### 性能优化
- **数据库连接池**: 显著提升数据库操作性能
- **优化的调度器**: 减少CPU和内存占用
- **异步任务执行**: 提高系统响应速度
- **智能缓存机制**: 减少重复数据库查询

### 日志管理
- 自动日志轮转和压缩
- 智能日志清理策略
- 日志统计和分析
- 支持日志搜索和过滤
- 可配置的日志保留策略

### 系统监控
- 实时系统资源监控（CPU、内存、磁盘）
- 任务执行性能统计
- 健康检查和告警机制
- 历史数据记录和分析
- 可视化监控面板

### 安全和稳定性
- 统一的错误处理机制
- API请求验证和速率限制
- 配置文件安全管理
- 系统健康自检功能
- 优雅的服务关闭和资源清理

## 新特性: 终止任务

添加了手动终止正在执行的任务功能。当任务正在执行时，界面上会显示终止按钮，点击可以强制停止任务的执行。任务被终止后会标记为"已终止"状态，并记录实际运行时长。

### 主要功能

- 执行中的任务会自动将"执行"按钮切换为"终止"按钮
- 点击终止按钮可以安全结束任务进程
- 终止后的任务会被标记为特殊状态"terminated"
- 终止操作会在日志中记录终止时间和原因

### 使用场景

- 当任务执行时间过长但又不想等待超时时
- 发现任务执行出错需要立即停止时
- 需要释放系统资源时

## TODO

- 修改错误重试机制

## 📦 安装和配置

### 环境要求

- Python 3.8+
- Windows 10/Linux/macOS
- 至少 512MB 可用内存
- 至少 1GB 可用磁盘空间

### 快速安装

1. **克隆项目**
```bash
git clone <repository-url>
cd task-manager
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env
```

4. **启动服务**
```bash
python app.py
```

### 配置说明

系统支持通过环境变量进行配置，主要配置项包括：

#### 数据库配置
```bash
DB_PATH=tasks.db                    # 数据库文件路径
LOG_DIR=log                         # 日志目录
MAX_LOGS=100                        # 最大日志文件数
```

#### Flask服务配置
```bash
FLASK_HOST=127.0.0.1               # 服务监听地址
FLASK_PORT=5001                    # 服务端口
FLASK_DEBUG=False                  # 调试模式
SECRET_KEY=your-secret-key         # 会话密钥
```

#### SMTP邮件配置
```bash
SMTP_HOST=smtp.example.com         # SMTP服务器
SMTP_PORT=465                      # SMTP端口
SMTP_USERNAME=<EMAIL>     # 邮箱用户名
SMTP_PASSWORD=password             # 邮箱密码
SMTP_USE_SSL=True                  # 是否使用SSL
SMTP_RECIPIENTS=<EMAIL>  # 默认收件人
```

#### 性能配置
```bash
MAX_CONCURRENT_TASKS=10            # 最大并发任务数
DEFAULT_MAX_RUNTIME=3600           # 默认任务超时时间
LOG_RETENTION_DAYS=30              # 日志保留天数
LOG_COMPRESS_AFTER_DAYS=7          # 日志压缩时间
```

## 🔧 使用方法

### 基本使用

```python
from task_manager import Task_manager

# 创建任务管理器实例（使用配置文件）
task_mgr = Task_manager()

# 添加定时任务
task_id = task_mgr.add_task(
    script_path='path/to/your/script.py',  # 脚本路径
    working_dir='path/to/working/dir',     # 工作目录
    cron_expression='*/5 * * * *',         # 每5分钟执行一次
    name="测试任务",                        # 任务名称
    max_runtime=3600,                      # 最长运行时间（秒）
    retry_on_error=True,                   # 错误时是否重试
    remark="这是一个测试任务"               # 任务备注
)

# 编辑任务
task_mgr.edit_task(
    task_id=task_id,
    cron_expression='*/10 * * * *',        # 修改为每10分钟执行一次
    max_runtime=1800                       # 修改最长运行时间为1800秒
)

# 手动立即运行任务
success, message = task_mgr.run_task_now(task_id)
print(message)  # 输出: 任务已启动 或 错误信息

# 获取任务日志
logs = task_mgr.get_task_logs(task_id=task_id, limit=10)

# 查看日志内容
log_content = task_mgr.get_log_content(logs[0]['log_file'])

# 停止任务管理器
task_mgr.stop()
```

### 添加通知功能

任务管理器支持两种通知方式：邮件通知和Webhook通知。您可以为任务添加一个或多个通知配置。

#### 初始化时设置SMTP配置

```python
# SMTP配置
smtp_config = {
    'host': 'smtp.example.com',
    'port': 465,
    'username': '<EMAIL>',
    'password': 'your_password',
    'sender': '<EMAIL>',
    'use_ssl': True
}

# 创建任务管理器实例，并在初始化时设置SMTP配置
task_mgr = Task_manager(
    db_path='tasks.db', 
    log_dir='log', 
    max_logs=100,
    smtp_config=smtp_config
)

# 更新SMTP配置
task_mgr.set_smtp_config(smtp_config)
```

#### 添加通知配置

```python
# 添加邮件通知（使用初始化时设置的SMTP配置）
email_config = {
    'recipients': ['<EMAIL>']
}

email_notification_id = task_mgr.add_notification(
    task_id=task_id,
    notification_type='email',
    config=email_config
)

# 添加Webhook通知
webhook_config = {
    'webhook_url': 'https://webhook.example.com/tasks',
    'method': 'POST',
    'headers': {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your_token_here'
    }
}

webhook_notification_id = task_mgr.add_notification(
    task_id=task_id,
    notification_type='webhook',
    config=webhook_config
)

# 获取任务的通知配置
notifications = task_mgr.get_notifications(task_id=task_id)

# 移除通知配置
task_mgr.remove_notification(email_notification_id)
```

### Crontab 表达式格式

任务管理器使用标准的crontab表达式来设置定时任务的执行时间。表达式格式为：

```
分 时 日 月 周
```

例如：
- `* * * * *` - 每分钟执行一次
- `*/5 * * * *` - 每5分钟执行一次
- `0 * * * *` - 每小时整点执行
- `0 9 * * 1-5` - 每个工作日（周一至周五）上午9点执行

## 📚 API 参考

### REST API 接口

系统提供完整的REST API接口，支持所有任务管理功能。

#### 任务管理

**创建任务**
```http
POST /api/tasks
Content-Type: application/json

{
    "name": "示例任务",
    "script_path": "python script.py",
    "working_dir": "/path/to/work",
    "cron_expression": "*/5 * * * *",
    "max_runtime": 3600,
    "retry_on_error": false,
    "remark": "任务描述"
}
```

**获取任务列表**
```http
GET /api/tasks
```

**更新任务**
```http
PUT /api/tasks/{task_id}
Content-Type: application/json

{
    "cron_expression": "*/10 * * * *",
    "max_runtime": 1800
}
```

**删除任务**
```http
DELETE /api/tasks/{task_id}
```

**立即执行任务**
```http
POST /api/tasks/{task_id}/run
```

**终止运行中的任务**
```http
POST /api/tasks/{task_id}/terminate
```

#### 监控和健康检查

**系统健康检查**
```http
GET /api/health
```

**获取系统指标**
```http
GET /api/metrics
```

**获取历史指标**
```http
GET /api/metrics/history?hours=24
```

**获取日志统计**
```http
GET /api/logs/statistics
```

### Python API

#### Task_manager 类

**初始化参数**:
- `db_path` - SQLite数据库路径（可选，默认使用配置文件）
- `log_dir` - 日志存储目录（可选，默认使用配置文件）
- `max_logs` - 最大日志文件数量（可选，默认使用配置文件）
- `smtp_config` - SMTP配置（可选，默认使用配置文件）

**主要方法**:
- `add_task()` - 添加新任务
- `edit_task()` - 编辑现有任务
- `run_task_now()` - 立即手动运行任务
- `terminate_task()` - 终止运行中的任务
- `remove_task()` - 移除任务（设为非活动）
- `get_tasks()` - 获取所有任务
- `get_task()` - 获取单个任务信息
- `get_task_logs()` - 获取任务执行日志
- `get_log_content()` - 获取日志文件内容
- `add_notification()` - 添加通知配置
- `remove_notification()` - 移除通知配置
- `get_notifications()` - 获取通知配置
- `stop()` - 停止任务管理器

### Notification 类

**初始化参数**:
- `smtp_config` - SMTP配置，用于邮件通知
- `log_level` - 日志级别，默认为 logging.INFO

**主要方法**:
- 发送邮件通知 `send_email(subject, content, recipients, smtp_config=None, html_content=None)`
- 发送Webhook通知 `send_webhook(webhook_url, payload, headers=None, method='POST', timeout=10)`
- 发送任务通知 `send_task_notification(task_info, notification_type, notification_config=None)`
- 设置SMTP配置 `set_smtp_config(smtp_config)`

## 运行示例

运行基本示例脚本:

```bash
python example.py
```

运行通知功能示例脚本:

```bash
python notification_example.py
python notification_example_advanced.py
```

## 🚀 性能优化

### v2.0 性能提升

相比v1.0版本，v2.0在以下方面有显著提升：

- **数据库性能**: 连接池机制提升30-50%的数据库操作性能
- **内存使用**: 优化的调度器减少20-30%的内存占用
- **响应时间**: 统一的API处理提升15-25%的响应速度
- **并发能力**: 支持更高的并发任务执行
- **系统稳定性**: 完善的错误处理和监控机制

### 性能调优建议

1. **数据库优化**
   - 定期清理过期日志数据
   - 适当调整数据库连接池大小
   - 启用WAL模式以提高并发性能

2. **内存优化**
   - 合理设置最大并发任务数
   - 定期压缩和清理日志文件
   - 监控系统内存使用情况

3. **磁盘优化**
   - 使用SSD存储以提高I/O性能
   - 定期清理临时文件和日志
   - 配置合适的日志轮转策略

## 🐳 部署指南

### Docker部署

1. **创建Dockerfile**
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5001

CMD ["python", "app.py"]
```

2. **构建和运行**
```bash
docker build -t task-manager .
docker run -d -p 5001:5001 --name task-manager task-manager
```

### 生产环境部署

1. **使用Gunicorn**
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5001 app:app
```

2. **使用Nginx反向代理**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

3. **使用Systemd服务**
```ini
[Unit]
Description=Task Manager Service
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/task-manager
ExecStart=/usr/bin/python3 app.py
Restart=always

[Install]
WantedBy=multi-user.target
```

## ⚠️ 注意事项

### 系统要求
- 任务管理器使用多线程进行任务调度和执行
- 建议在生产环境中使用专用的数据库服务器
- 确保有足够的磁盘空间存储日志文件
- 定期备份数据库文件

### 安全建议
- 使用环境变量管理敏感配置信息
- 定期更新依赖包以修复安全漏洞
- 配置防火墙限制访问端口
- 启用HTTPS加密传输

### 监控和维护
- 定期检查系统健康状态
- 监控磁盘空间和内存使用
- 设置日志轮转和清理策略
- 配置邮件或Webhook通知以及时发现问题