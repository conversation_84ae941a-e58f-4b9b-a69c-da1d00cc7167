
# -*- coding: utf-8 -*-
"""
测试任务脚本
用于测试日志输出和编码处理
"""
import time
import datetime
import sys
import os

print(f"任务开始执行，时间: {datetime.datetime.now()}")
print("这是一个测试任务")

# 显示环境信息
print(f"Python版本: {sys.version}")
print(f"默认编码: {sys.getdefaultencoding()}")
print(f"PYTHONIOENCODING: {os.environ.get('PYTHONIOENCODING', '未设置')}")

# 测试各种字符输出
print("=== 编码测试 ===")
print("中文字符: 你好世界")
print("Unicode字符: ❤️ 🎉 🚀 ⭐")
print("特殊符号: ©️ ®️ ™️")

# 测试\r字符处理（应该像控制台那样覆盖）
print("=== \\r字符测试 ===")
print("正常输出: 123456")

# 这应该只显示 "123"（555被123覆盖）
print("555\r123", end="")
print()  # 换行

# 这应该只显示 "ccc"（aaa和bbb被覆盖）
print("aaa\rbbb\rccc", end="")
print()  # 换行

# 测试混合内容
print("=== 混合测试 ===")
print("Hello\r世界❤️", end="")
print()  # 换行

# 测试进度条效果
print("=== 进度条测试 ===")
for i in range(5):
    print(f"进度: {i+1}/5 {'█' * (i+1)}{'░' * (4-i)}\r", end="")
    time.sleep(0.5)
print()  # 最后换行

# 测试长文本
print("=== 长文本测试 ===")
long_text = "这是一个很长的文本" * 5 + "❤️"
print(f"长文本: {long_text}")

print("任务执行完成")
print(f"任务结束，时间: {datetime.datetime.now()}")

# 测试文件写入
with open('test.txt', 'w', encoding='utf-8') as fp:
    fp.write('Hello World ❤️ 你好世界\n')
    fp.write('测试\\r字符处理\n')

print("测试文件已创建: test.txt")
