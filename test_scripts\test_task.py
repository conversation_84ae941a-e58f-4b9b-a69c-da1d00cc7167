
# -*- coding: utf-8 -*-
"""
测试任务脚本
用于测试日志输出和编码处理
"""
import time
import datetime
import sys

# 确保输出使用UTF-8编码
if sys.platform == 'win32':
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

print(f"任务开始执行，时间: {datetime.datetime.now()}")
print("这是一个测试任务")

# 测试各种字符输出
print("=== 编码测试 ===")
print("中文字符: 你好世界")
print("Unicode字符: ❤️ 🎉 🚀 ⭐")
print("特殊符号: ©️ ®️ ™️")

# 测试\r字符处理
print("=== \\r字符测试 ===")
print("正常输出: 123456")
print("包含\\r: 555\\r123")  # 这应该显示为两行
print("多个\\r: aaa\\rbbb\\rccc")

# 测试混合内容
print("=== 混合测试 ===")
print("混合内容: Hello\\r世界❤️\\rWorld🌍")

# 测试长文本
print("=== 长文本测试 ===")
long_text = "这是一个很长的文本" * 10 + "❤️"
print(f"长文本: {long_text}")

print("任务执行了5秒钟")
# time.sleep(5)  # 取消注释以测试长时间运行

print(f"任务结束，时间: {datetime.datetime.now()}")

# 测试文件写入
with open('test.txt', 'w', encoding='utf-8') as fp:
    fp.write('Hello World ❤️ 你好世界\n')
    fp.write('测试\\r字符处理\n')

print("测试文件已创建: test.txt")
