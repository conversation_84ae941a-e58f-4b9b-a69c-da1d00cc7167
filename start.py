#!/usr/bin/env python3
"""
任务管理器启动脚本
提供便捷的启动和配置功能
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path


def check_dependencies():
    """检查依赖是否已安装"""
    try:
        import croniter
        import flask
        import psutil
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_encoding():
    """检查编码环境"""
    import sys
    import locale

    print("检查编码环境...")
    print(f"Python版本: {sys.version}")
    print(f"默认编码: {sys.getdefaultencoding()}")
    print(f"文件系统编码: {sys.getfilesystemencoding()}")
    print(f"系统locale: {locale.getpreferredencoding()}")

    # 检查环境变量
    pythonioencoding = os.environ.get('PYTHONIOENCODING', '')
    pythonutf8 = os.environ.get('PYTHONUTF8', '')

    print(f"PYTHONIOENCODING: {pythonioencoding}")
    print(f"PYTHONUTF8: {pythonutf8}")

    # Windows特殊检查
    if sys.platform == 'win32':
        print("检测到Windows系统")
        if not pythonioencoding:
            print("⚠ 建议设置 PYTHONIOENCODING=utf-8")
        if not pythonutf8:
            print("⚠ 建议设置 PYTHONUTF8=1")

        # 测试Unicode输出
        try:
            test_chars = "测试Unicode: ❤️ 🎉 🚀"
            print(f"Unicode测试: {test_chars}")
            print("✓ Unicode字符显示正常")
        except UnicodeEncodeError as e:
            print(f"✗ Unicode编码错误: {e}")
            print("建议使用 start_utf8.bat 启动")
            return False

    print("✓ 编码环境检查完成")
    return True


def check_config():
    """检查配置文件"""
    env_file = Path('.env')
    if not env_file.exists():
        print("⚠ 未找到配置文件 .env")
        print("正在创建默认配置文件...")
        
        # 复制示例配置文件
        example_file = Path('.env.example')
        if example_file.exists():
            import shutil
            shutil.copy(example_file, env_file)
            print("✓ 已创建默认配置文件 .env")
            print("请编辑 .env 文件以配置您的设置")
        else:
            print("✗ 未找到配置模板文件 .env.example")
            return False
    else:
        print("✓ 配置文件已存在")
    
    return True


def setup_database():
    """初始化数据库"""
    try:
        from config import config
        from database import DatabasePool
        
        print(f"正在初始化数据库: {config.DB_PATH}")
        
        # 创建数据库连接池，这会自动初始化数据库表
        db_pool = DatabasePool(config.DB_PATH)
        db_pool.close_all()
        
        print("✓ 数据库初始化完成")
        return True
        
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        return False


def create_directories():
    """创建必要的目录"""
    try:
        from config import config
        
        # 创建日志目录
        log_dir = Path(config.LOG_DIR)
        log_dir.mkdir(parents=True, exist_ok=True)
        print(f"✓ 日志目录已创建: {log_dir}")
        
        # 创建数据库目录
        db_path = Path(config.DB_PATH)
        db_path.parent.mkdir(parents=True, exist_ok=True)
        print(f"✓ 数据库目录已创建: {db_path.parent}")
        
        return True
        
    except Exception as e:
        print(f"✗ 目录创建失败: {e}")
        return False


def start_server(host=None, port=None, debug=False):
    """启动服务器"""
    try:
        from config import config
        
        # 使用传入的参数或配置文件中的默认值
        host = host or config.FLASK_HOST
        port = port or config.FLASK_PORT
        debug = debug or config.FLASK_DEBUG
        
        print(f"正在启动任务管理器...")
        print(f"服务地址: http://{host}:{port}")
        print("按 Ctrl+C 停止服务")
        
        # 启动Flask应用
        from app import app
        app.run(host=host, port=port, debug=debug)
        
    except KeyboardInterrupt:
        print("\n正在停止服务...")
    except Exception as e:
        print(f"✗ 服务启动失败: {e}")
        return False
    
    return True


def run_tests():
    """运行测试"""
    try:
        print("正在运行测试...")
        result = subprocess.run([sys.executable, '-m', 'pytest', 'tests/', '-v'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 所有测试通过")
            print(result.stdout)
        else:
            print("✗ 测试失败")
            print(result.stderr)
        
        return result.returncode == 0
        
    except FileNotFoundError:
        print("⚠ pytest 未安装，跳过测试")
        return True
    except Exception as e:
        print(f"✗ 测试运行失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='任务管理器启动脚本')
    parser.add_argument('--host', default=None, help='服务监听地址')
    parser.add_argument('--port', type=int, default=None, help='服务端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--init', action='store_true', help='仅初始化系统')
    parser.add_argument('--test', action='store_true', help='运行测试')
    parser.add_argument('--check', action='store_true', help='检查系统状态')
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("任务管理器 v2.0 启动脚本")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    # 检查编码环境
    if not check_encoding():
        sys.exit(1)

    # 检查配置
    if not check_config():
        sys.exit(1)
    
    # 创建目录
    if not create_directories():
        sys.exit(1)
    
    # 初始化数据库
    if not setup_database():
        sys.exit(1)
    
    # 如果只是检查系统状态
    if args.check:
        print("✓ 系统检查完成，所有组件正常")
        return
    
    # 如果只是初始化
    if args.init:
        print("✓ 系统初始化完成")
        return
    
    # 运行测试
    if args.test:
        if not run_tests():
            sys.exit(1)
        return
    
    # 启动服务器
    print("\n" + "=" * 50)
    print("启动服务器")
    print("=" * 50)
    
    if not start_server(args.host, args.port, args.debug):
        sys.exit(1)


if __name__ == '__main__':
    main()
