version: '3.8'

services:
  task-manager:
    build: .
    ports:
      - "5001:5001"
    volumes:
      - ./data:/app/data
      - ./log:/app/log
    environment:
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=5001
      - DB_PATH=/app/data/tasks.db
      - LOG_DIR=/app/log
      - MAX_LOGS=100
      - MAX_CONCURRENT_TASKS=10
      - MONITORING_ENABLED=true
      - LOG_RETENTION_DAYS=30
      - LOG_COMPRESS_AFTER_DAYS=7
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  task_data:
  task_logs:
