# 需求清单

## 待完成

- 启动任务之后不要弹出日志,点击任务状态可以查看最新日志
- 搜索框添加清空文本按钮
- 执行中的任务,手动执行按钮转换成停止按钮,点击停止按钮可以终止任务让后恢复手动执行按钮
- 任务分组
- 日志查询功能
- 启动task类的时候判断下日志是否被清理,数据需要对日志表增加一个deleted自动,清理后的行数据下次不去check
- 单个任务有自己的通知配置
- 可以设置脚本的环境变量
- 增加用户验证 权限限制....
- 允许只读用户访问查看指定任务的运行和日志
- 可以把错误的日志标记成已处理然后不会出现在失败列表内
- BUG 立即执行 弹出的任务详情刷新的时候不会滚动到最下方
- 从隐藏到显示浏览器执行一个刷新。
- ​任务列表排序根据运行总在最前。失败在第二.
- 弹窗的情况下 点击弹窗外的区域就触发关闭弹窗
- 显示当前机器的cpu和内存信息封装到一起(和面板数据)
- 日志\r问题
- 任务列表增加筛选栏 筛选状态
- 任务列表增加分组 自定义排序
- 队列因子,添加任务可以设置队列因子(为空不需要),同一队列因子下的任务可以不并发执行,不同队列因子下的任务可以并发执行

## 已完成

- 日志详情获取完自动滚动到最下面
- BUG 日志删除逻辑有问题
- 任务信息为最近24小时
- 新增终止任务功能 (2025-05-23) - 实现执行中任务的强制终止和状态标记
