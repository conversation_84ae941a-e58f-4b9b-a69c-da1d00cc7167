
2025-07-30 16:35:49 任务开始执行
任务开始执行，时间: 2025-07-30 16:35:49.870853
这是一个测试任务
Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
默认编码: utf-8
PYTHONIOENCODING: 未设置
=== 编码测试 ===
中文字符: 你好世界
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\需求说明\任务系统2\test_scripts\test_task.py", line 23, in <module>
    print("Unicode字符: \u2764\ufe0f \U0001f389 \U0001f680 \u2b50")
UnicodeEncodeError: 'gbk' codec can't encode character '\u2764' in position 11: illegal multibyte sequence
