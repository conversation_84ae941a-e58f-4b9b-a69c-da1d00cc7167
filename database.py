"""
数据库连接池和操作封装模块
提供高效的数据库连接管理和常用操作
"""
import sqlite3
import threading
import queue
import logging
from contextlib import contextmanager
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path


class DatabasePool:
    """SQLite数据库连接池"""
    
    def __init__(self, db_path: str, max_connections: int = 10):
        """
        初始化数据库连接池
        
        参数:
            db_path: 数据库文件路径
            max_connections: 最大连接数
        """
        self.db_path = db_path
        self.max_connections = max_connections
        self._pool = queue.Queue(maxsize=max_connections)
        self._lock = threading.Lock()
        self._created_connections = 0
        
        # 确保数据库文件目录存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库表结构
        self._init_database()
        
        # 预创建一些连接
        self._create_initial_connections()
    
    def _create_connection(self) -> sqlite3.Connection:
        """创建新的数据库连接"""
        conn = sqlite3.connect(
            self.db_path,
            check_same_thread=False,
            timeout=30.0
        )
        conn.row_factory = sqlite3.Row
        # 启用外键约束
        conn.execute("PRAGMA foreign_keys = ON")
        # 设置WAL模式以提高并发性能
        conn.execute("PRAGMA journal_mode = WAL")
        return conn
    
    def _create_initial_connections(self):
        """预创建初始连接"""
        initial_count = min(3, self.max_connections)
        for _ in range(initial_count):
            try:
                conn = self._create_connection()
                self._pool.put(conn, block=False)
                self._created_connections += 1
            except queue.Full:
                break
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器
        
        使用方式:
            with db_pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM tasks")
        """
        conn = None
        try:
            # 尝试从池中获取连接
            try:
                conn = self._pool.get(block=False)
            except queue.Empty:
                # 池中没有可用连接，创建新连接
                with self._lock:
                    if self._created_connections < self.max_connections:
                        conn = self._create_connection()
                        self._created_connections += 1
                    else:
                        # 等待可用连接
                        conn = self._pool.get(timeout=10)
            
            yield conn
            
        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            raise e
        finally:
            if conn:
                try:
                    # 将连接放回池中
                    self._pool.put(conn, block=False)
                except queue.Full:
                    # 池已满，关闭连接
                    conn.close()
                    with self._lock:
                        self._created_connections -= 1
    
    def _init_database(self):
        """初始化数据库表结构"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建任务表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    script_path TEXT NOT NULL,
                    working_dir TEXT NOT NULL,
                    max_runtime INTEGER NOT NULL DEFAULT 3600,
                    retry_on_error INTEGER NOT NULL DEFAULT 0,
                    cron_expression TEXT NOT NULL,
                    remark TEXT DEFAULT '',
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建任务执行记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS task_runs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id INTEGER NOT NULL,
                    start_time TIMESTAMP NOT NULL,
                    end_time TIMESTAMP,
                    duration REAL,
                    status TEXT NOT NULL DEFAULT 'running',
                    log_file TEXT,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
                )
            ''')
            
            # 创建通知配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS notification_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id INTEGER NOT NULL,
                    notification_type TEXT NOT NULL,
                    config TEXT NOT NULL,
                    is_active INTEGER DEFAULT 1,
                    notify_on_error INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
                )
            ''')
            
            # 创建索引以提高查询性能
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tasks_active ON tasks(is_active)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_task_runs_task_id ON task_runs(task_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_task_runs_start_time ON task_runs(start_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_notification_config_task_id ON notification_config(task_id)')
            
            conn.commit()
    
    def close_all(self):
        """关闭所有连接"""
        while not self._pool.empty():
            try:
                conn = self._pool.get(block=False)
                conn.close()
            except queue.Empty:
                break
        self._created_connections = 0


class DatabaseManager:
    """数据库操作管理器"""
    
    def __init__(self, db_pool: DatabasePool):
        """
        初始化数据库管理器
        
        参数:
            db_pool: 数据库连接池实例
        """
        self.db_pool = db_pool
        self.logger = logging.getLogger(__name__)
    
    def execute_query(self, query: str, params: Tuple = ()) -> List[Dict[str, Any]]:
        """
        执行查询语句并返回结果
        
        参数:
            query: SQL查询语句
            params: 查询参数
            
        返回:
            查询结果列表
        """
        try:
            with self.db_pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"查询执行失败: {query}, 参数: {params}, 错误: {e}")
            raise
    
    def execute_update(self, query: str, params: Tuple = ()) -> int:
        """
        执行更新语句并返回影响的行数
        
        参数:
            query: SQL更新语句
            params: 更新参数
            
        返回:
            影响的行数
        """
        try:
            with self.db_pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            self.logger.error(f"更新执行失败: {query}, 参数: {params}, 错误: {e}")
            raise
    
    def execute_insert(self, query: str, params: Tuple = ()) -> int:
        """
        执行插入语句并返回新记录的ID
        
        参数:
            query: SQL插入语句
            params: 插入参数
            
        返回:
            新记录的ID
        """
        try:
            with self.db_pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            self.logger.error(f"插入执行失败: {query}, 参数: {params}, 错误: {e}")
            raise
