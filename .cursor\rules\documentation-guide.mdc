---
description: 
globs: 
alwaysApply: false
---
# 文档指南

## 新特性和新功能记录

所有新开发的特性和功能都必须记录在 [README.md](mdc:README.md) 文件中。

### 规范要求

1. 每个新特性应当有一个清晰的标题（H2）
2. 特性描述应当包含其目的和使用场景
3. 如果有API变更，应当提供示例代码
4. 对于UI变更，可以考虑添加截图说明

### 示例格式

```markdown
## 新特性: 邮件通知

添加了任务执行后的邮件通知功能。当任务执行失败时，系统会自动发送邮件通知管理员。

### 配置示例

```python
smtp_config = {
    'host': 'smtp.example.com',
    'port': 465,
    'username': '<EMAIL>',
    'password': '******',
    'sender': '<EMAIL>',
    'use_ssl': True,
    'recipients': ['<EMAIL>']
}
```
```

遵循此规则能确保所有团队成员都能了解项目的最新功能和变更。

