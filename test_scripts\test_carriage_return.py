# -*- coding: utf-8 -*-
"""
专门测试\r字符处理的脚本
验证\r字符是否像控制台那样覆盖前面的内容
"""
import time
import sys

print("=== \\r字符处理测试 ===")
print("测试开始")

# 测试1: 基本覆盖
print("测试1: 基本覆盖")
print("原始内容\r新内容", end="")
print()  # 换行
print("期望结果: 新内容")
print()

# 测试2: 多次覆盖
print("测试2: 多次覆盖")
print("第一次\r第二次\r第三次", end="")
print()  # 换行
print("期望结果: 第三次")
print()

# 测试3: 部分覆盖
print("测试3: 部分覆盖")
print("123456\r999", end="")
print()  # 换行
print("期望结果: 999456")
print()

# 测试4: 中文字符覆盖
print("测试4: 中文字符覆盖")
print("你好世界\r再见", end="")
print()  # 换行
print("期望结果: 再见世界")
print()

# 测试5: Unicode字符覆盖
print("测试5: Unicode字符覆盖")
print("❤️🎉🚀\r⭐", end="")
print()  # 换行
print("期望结果: ⭐🎉🚀")
print()

# 测试6: 进度条模拟
print("测试6: 进度条模拟")
for i in range(6):
    progress = "█" * i + "░" * (5-i)
    print(f"进度: {progress} {i*20}%\r", end="")
    time.sleep(0.3)
print()  # 最后换行
print("期望结果: 进度: █████ 100%")
print()

# 测试7: 混合内容
print("测试7: 混合内容")
print("Hello World\r你好❤️", end="")
print()  # 换行
print("期望结果: 你好❤️orld")
print()

# 测试8: 空字符串覆盖
print("测试8: 空字符串覆盖")
print("有内容\r", end="")
print()  # 换行
print("期望结果: (空行)")
print()

# 测试9: 长字符串覆盖短字符串
print("测试9: 长字符串覆盖短字符串")
print("短\r这是一个很长的字符串", end="")
print()  # 换行
print("期望结果: 这是一个很长的字符串")
print()

# 测试10: 短字符串覆盖长字符串
print("测试10: 短字符串覆盖长字符串")
print("这是一个很长的字符串\r短", end="")
print()  # 换行
print("期望结果: 短个很长的字符串")
print()

print("=== 测试完成 ===")
print("如果\\r字符处理正确，上面的实际输出应该与期望结果一致")
