---
description: 
globs: 
alwaysApply: true
---
# 任务管理系统 - 项目结构

## 核心文件

- [app.py](mdc:app.py) - Flask Web应用主入口文件，包含所有API路由和Web界面控制
- [task_manager.py](mdc:task_manager.py) - 任务管理器核心类，负责任务的创建、执行、监控和日志管理
- [notification.py](mdc:notification.py) - 通知系统，支持邮件和Webhook通知方式
- [main_script.py](mdc:main_script.py) - 应用程序主脚本，用于启动任务管理系统

## 前端文件

- [templates/index.html](mdc:templates/index.html) - 单页面Web应用界面模板
- [static/css](mdc:static/css) - 样式文件目录
- [static/js](mdc:static/js) - JavaScript脚本目录

## 数据存储

- [tasks.db](mdc:tasks.db) - SQLite数据库文件，存储任务信息和执行记录

## 日志文件

- [log](mdc:log) - 日志目录，存储所有任务执行的日志文件

## 测试脚本

- [test_scripts](mdc:test_scripts) - 测试任务脚本示例目录
- [test_task.py](mdc:test_task.py) - 任务测试脚本
- [tset.py](mdc:tset.py) - 测试脚本

## 文档

- [README.md](mdc:README.md) - 系统使用说明和API文档
- [TODO.md](mdc:TODO.md) - 待办事项列表

### 项目的所有待办事项应当记录在 @TODO.md 文件中，并按照以下规则管理。

1. 待办事项应当在"待完成"部分列出
2. 当任务完成后，应当从"待完成"部分移动到"已完成"部分

## 项目依赖

- [requirements.txt](mdc:requirements.txt) - Python依赖包列表

