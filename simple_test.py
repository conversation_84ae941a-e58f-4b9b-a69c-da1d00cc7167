#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试\r字符和实时日志
"""
import os
import sys
import tempfile
import time

# 设置UTF-8环境
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'
os.environ['MONITORING_ENABLED'] = 'False'

sys.path.insert(0, '.')
from task_manager import Task_manager

def main():
    print("简单测试\\r字符和实时日志")
    print("=" * 40)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建任务管理器
        task_mgr = Task_manager(
            db_path=os.path.join(temp_dir, 'test.db'),
            log_dir=os.path.join(temp_dir, 'logs')
        )
        
        # 创建简单测试脚本
        test_script = os.path.join(temp_dir, 'simple_test.py')
        with open(test_script, 'w', encoding='utf-8') as f:
            f.write('''# -*- coding: utf-8 -*-
import time

print("开始测试")
time.sleep(1)

print("测试\\r覆盖:")
print("555\\r123", end="")
print()

print("测试完成")
''')
        
        # 添加任务
        task_id = task_mgr.add_task(
            script_path=f'python "{test_script}"',
            working_dir=temp_dir,
            cron_expression='*/5 * * * *',
            name='简单测试',
            max_runtime=10
        )
        
        print(f"任务已添加，ID: {task_id}")
        
        # 执行任务
        success, message = task_mgr.run_task_now(task_id)
        print(f"任务执行: {success}, {message}")
        
        if success:
            # 等待任务完成
            time.sleep(5)
            
            # 获取日志
            logs = task_mgr.get_task_logs(task_id, limit=1)
            if logs:
                log_content = task_mgr.get_log_content(logs[0]['id'])
                print("\n日志内容:")
                print("-" * 30)
                print(log_content)
                print("-" * 30)
                
                # 分析日志
                lines = [line.strip() for line in log_content.split('\n') if line.strip()]
                print("\n分析结果:")
                
                # 查找555\r123的处理结果
                found_555 = any("555" in line for line in lines)
                found_123 = any(line == "123" for line in lines)
                
                if found_123 and not found_555:
                    print("✅ \\r字符覆盖成功：555\\r123 正确显示为 123")
                elif found_123 and found_555:
                    print("⚠️ \\r字符部分成功：显示了 555 和 123 两行")
                else:
                    print("❌ \\r字符处理失败")
                
                print(f"日志行数: {len(lines)}")
                for i, line in enumerate(lines, 1):
                    print(f"  {i}: {repr(line)}")
            else:
                print("❌ 未找到日志")
        else:
            print("❌ 任务执行失败")
        
        # 确保正确关闭
        task_mgr.stop()

if __name__ == '__main__':
    main()
    print("\n测试完成！")
