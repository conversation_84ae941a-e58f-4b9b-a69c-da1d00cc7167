"""
配置管理模块
用于管理应用程序的各种配置参数
"""
import os
from typing import Dict, Any, Optional


class Config:
    """应用程序配置类"""
    
    def __init__(self):
        """初始化配置"""
        self.load_config()
    
    def load_config(self):
        """加载配置信息"""
        # 数据库配置
        self.DB_PATH = os.getenv('DB_PATH', 'tasks.db')
        self.LOG_DIR = os.getenv('LOG_DIR', 'log')
        self.MAX_LOGS = int(os.getenv('MAX_LOGS', '100'))
        
        # Flask配置
        self.FLASK_HOST = os.getenv('FLASK_HOST', '127.0.0.1')
        self.FLASK_PORT = int(os.getenv('FLASK_PORT', '5008'))
        self.FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
        
        # SMTP配置
        self.SMTP_CONFIG = self._load_smtp_config()
        
        # 安全配置
        self.SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
        
        # 任务执行配置
        self.DEFAULT_MAX_RUNTIME = int(os.getenv('DEFAULT_MAX_RUNTIME', '3600'))
        self.MAX_CONCURRENT_TASKS = int(os.getenv('MAX_CONCURRENT_TASKS', '10'))
    
    def _load_smtp_config(self) -> Optional[Dict[str, Any]]:
        """加载SMTP配置"""
        smtp_host = os.getenv('SMTP_HOST')
        if not smtp_host:
            return None
            
        return {
            'host': smtp_host,
            'port': int(os.getenv('SMTP_PORT', '465')),
            'username': os.getenv('SMTP_USERNAME'),
            'password': os.getenv('SMTP_PASSWORD'),
            'sender': os.getenv('SMTP_SENDER'),
            'use_ssl': os.getenv('SMTP_USE_SSL', 'True').lower() == 'true',
            'recipients': os.getenv('SMTP_RECIPIENTS', '').split(',') if os.getenv('SMTP_RECIPIENTS') else []
        }
    
    def get_smtp_config(self) -> Optional[Dict[str, Any]]:
        """获取SMTP配置"""
        return self.SMTP_CONFIG
    
    def validate_config(self) -> bool:
        """验证配置的有效性"""
        # 验证必要的配置项
        if not self.DB_PATH:
            raise ValueError("数据库路径不能为空")
        
        if not self.LOG_DIR:
            raise ValueError("日志目录不能为空")
        
        if self.MAX_LOGS <= 0:
            raise ValueError("最大日志数量必须大于0")
        
        return True


# 全局配置实例
config = Config()
