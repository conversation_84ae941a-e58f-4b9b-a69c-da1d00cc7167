"""
日志管理模块
提供日志轮转、压缩、清理等功能
"""
import os
import gzip
import shutil
import threading
import time
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class LogFile:
    """日志文件信息"""
    path: Path
    size: int
    created_time: datetime
    task_id: int
    is_compressed: bool = False


class LogManager:
    """日志管理器"""
    
    def __init__(self, log_dir: str, max_logs: int = 100, 
                 max_log_size: int = 10 * 1024 * 1024,  # 10MB
                 retention_days: int = 30,
                 compress_after_days: int = 7):
        """
        初始化日志管理器
        
        参数:
            log_dir: 日志目录
            max_logs: 最大日志文件数
            max_log_size: 单个日志文件最大大小（字节）
            retention_days: 日志保留天数
            compress_after_days: 多少天后压缩日志
        """
        self.log_dir = Path(log_dir)
        self.max_logs = max_logs
        self.max_log_size = max_log_size
        self.retention_days = retention_days
        self.compress_after_days = compress_after_days
        
        # 确保日志目录存在
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 线程控制
        self._stop_event = threading.Event()
        self._cleanup_thread: Optional[threading.Thread] = None
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        # 启动清理线程
        self.start_cleanup_thread()
    
    def start_cleanup_thread(self):
        """启动日志清理线程"""
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            return
        
        self._cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self._cleanup_thread.start()
        self.logger.info("日志清理线程已启动")
    
    def stop_cleanup_thread(self):
        """停止日志清理线程"""
        self._stop_event.set()
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=5)
        self.logger.info("日志清理线程已停止")
    
    def create_log_file(self, task_id: int) -> Path:
        """
        为任务创建新的日志文件
        
        参数:
            task_id: 任务ID
            
        返回:
            日志文件路径
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_filename = f"{task_id}_{timestamp}.log"
        log_path = self.log_dir / log_filename
        
        # 创建空日志文件
        log_path.touch()
        
        self.logger.debug(f"为任务 {task_id} 创建日志文件: {log_path}")
        return log_path
    
    def get_log_files(self, task_id: Optional[int] = None) -> List[LogFile]:
        """
        获取日志文件列表
        
        参数:
            task_id: 任务ID，如果为None则返回所有日志文件
            
        返回:
            日志文件信息列表
        """
        log_files = []
        
        for file_path in self.log_dir.iterdir():
            if not file_path.is_file():
                continue
            
            # 解析文件名获取任务ID
            try:
                filename = file_path.name
                if filename.endswith('.log.gz'):
                    # 压缩文件
                    task_id_str = filename.split('_')[0]
                    file_task_id = int(task_id_str)
                    is_compressed = True
                elif filename.endswith('.log'):
                    # 普通日志文件
                    task_id_str = filename.split('_')[0]
                    file_task_id = int(task_id_str)
                    is_compressed = False
                else:
                    continue
                
                # 如果指定了task_id，则过滤
                if task_id is not None and file_task_id != task_id:
                    continue
                
                # 获取文件信息
                stat = file_path.stat()
                log_file = LogFile(
                    path=file_path,
                    size=stat.st_size,
                    created_time=datetime.fromtimestamp(stat.st_ctime),
                    task_id=file_task_id,
                    is_compressed=is_compressed
                )
                log_files.append(log_file)
                
            except (ValueError, IndexError):
                # 文件名格式不正确，跳过
                continue
        
        # 按创建时间排序
        log_files.sort(key=lambda x: x.created_time, reverse=True)
        return log_files
    
    def read_log_content(self, log_path: Path) -> str:
        """
        读取日志文件内容
        
        参数:
            log_path: 日志文件路径
            
        返回:
            日志内容
        """
        try:
            if log_path.suffix == '.gz':
                # 压缩文件
                with gzip.open(log_path, 'rt', encoding='utf-8', errors='replace') as f:
                    return f.read()
            else:
                # 普通文件
                with open(log_path, 'r', encoding='utf-8', errors='replace') as f:
                    return f.read()
        except Exception as e:
            self.logger.error(f"读取日志文件 {log_path} 失败: {e}")
            return f"读取日志文件时发生错误: {str(e)}"
    
    def compress_log_file(self, log_path: Path) -> bool:
        """
        压缩日志文件
        
        参数:
            log_path: 日志文件路径
            
        返回:
            是否压缩成功
        """
        if log_path.suffix == '.gz':
            return True  # 已经是压缩文件
        
        try:
            compressed_path = log_path.with_suffix(log_path.suffix + '.gz')
            
            with open(log_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # 删除原文件
            log_path.unlink()
            
            self.logger.info(f"日志文件 {log_path} 已压缩为 {compressed_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"压缩日志文件 {log_path} 失败: {e}")
            return False
    
    def delete_log_file(self, log_path: Path) -> bool:
        """
        删除日志文件
        
        参数:
            log_path: 日志文件路径
            
        返回:
            是否删除成功
        """
        try:
            log_path.unlink()
            self.logger.info(f"日志文件 {log_path} 已删除")
            return True
        except Exception as e:
            self.logger.error(f"删除日志文件 {log_path} 失败: {e}")
            return False
    
    def cleanup_logs(self):
        """清理日志文件"""
        try:
            log_files = self.get_log_files()
            current_time = datetime.now()
            
            # 1. 删除过期的日志文件
            expired_files = [
                log_file for log_file in log_files
                if (current_time - log_file.created_time).days > self.retention_days
            ]
            
            for log_file in expired_files:
                self.delete_log_file(log_file.path)
            
            # 2. 压缩旧的日志文件
            old_files = [
                log_file for log_file in log_files
                if (not log_file.is_compressed and 
                    (current_time - log_file.created_time).days > self.compress_after_days)
            ]
            
            for log_file in old_files:
                self.compress_log_file(log_file.path)
            
            # 3. 如果日志文件数量超过限制，删除最旧的文件
            remaining_files = self.get_log_files()
            if len(remaining_files) > self.max_logs:
                files_to_delete = remaining_files[self.max_logs:]
                for log_file in files_to_delete:
                    self.delete_log_file(log_file.path)
            
            # 4. 检查大文件并进行轮转
            self._rotate_large_files()
            
            self.logger.info(f"日志清理完成，删除了 {len(expired_files)} 个过期文件，"
                           f"压缩了 {len(old_files)} 个旧文件")
            
        except Exception as e:
            self.logger.error(f"日志清理失败: {e}")
    
    def _rotate_large_files(self):
        """轮转大文件"""
        log_files = self.get_log_files()
        
        for log_file in log_files:
            if (not log_file.is_compressed and 
                log_file.size > self.max_log_size):
                
                # 压缩大文件
                self.compress_log_file(log_file.path)
    
    def _cleanup_loop(self):
        """清理循环"""
        while not self._stop_event.is_set():
            try:
                self.cleanup_logs()
                
                # 每小时执行一次清理
                self._stop_event.wait(timeout=3600)
                
            except Exception as e:
                self.logger.error(f"日志清理循环出错: {e}")
                self._stop_event.wait(timeout=60)
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """
        获取日志统计信息
        
        返回:
            日志统计信息字典
        """
        log_files = self.get_log_files()
        
        total_size = sum(log_file.size for log_file in log_files)
        compressed_count = sum(1 for log_file in log_files if log_file.is_compressed)
        
        # 按任务ID分组统计
        task_stats = {}
        for log_file in log_files:
            task_id = log_file.task_id
            if task_id not in task_stats:
                task_stats[task_id] = {'count': 0, 'size': 0}
            task_stats[task_id]['count'] += 1
            task_stats[task_id]['size'] += log_file.size
        
        return {
            'total_files': len(log_files),
            'total_size': total_size,
            'compressed_files': compressed_count,
            'uncompressed_files': len(log_files) - compressed_count,
            'task_statistics': task_stats,
            'oldest_log': min(log_files, key=lambda x: x.created_time).created_time if log_files else None,
            'newest_log': max(log_files, key=lambda x: x.created_time).created_time if log_files else None
        }
