from task_manager import Task_manager
import time
import os
from pathlib import Path

def main():
    # SMTP配置
    smtp_config = {
        'host': 'smtp.feishu.cn',
        'port': 465,
        'username': '<EMAIL>',
        'password': 'NvmTv3B9xricYjBn',
        'sender': '<EMAIL>',
        'use_ssl': True,
        'recipients': ['<EMAIL>']
    }
    task_mgr = Task_manager(db_path='tasks.db', log_dir='log', max_logs=100, smtp_config=smtp_config)
    task_id = task_mgr.add_task(
        name='巨量分时',
        script_path=str(r'C:\Users\<USER>\Desktop\项目\虚拟环境\api\Scripts\python.exe -u job\巨量分时数据.py'),
        working_dir=str(r'C:\Users\<USER>\Desktop\项目\API文件夹'),
        cron_expression='1 6,9-23 * * *',  # 每分钟执行一次
        max_runtime=60*8,  # 最长运行8分钟
        retry_on_error=True  # 错误时重试
    )
    task_mgr.add_notification(task_id,notification_type='email',config={'recipients': ['<EMAIL>']})
    task_mgr.run_task_now(task_id)
    # 主线程等待
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止任务管理器...")
    finally:
        # 停止任务管理器
        task_mgr.stop()
        print("任务管理器已停止")

if __name__ == "__main__":
    main()

