#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试编码和\r字符修复
"""
import os
import sys
import tempfile
import time

# 设置UTF-8环境
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'
os.environ['MONITORING_ENABLED'] = 'False'

sys.path.insert(0, '.')
from task_manager import Task_manager

def main():
    print("快速测试编码和\\r字符修复")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建任务管理器
        task_mgr = Task_manager(
            db_path=os.path.join(temp_dir, 'test.db'),
            log_dir=os.path.join(temp_dir, 'logs')
        )
        
        # 创建测试脚本
        test_script = os.path.join(temp_dir, 'test.py')
        with open(test_script, 'w', encoding='utf-8') as f:
            f.write('''# -*- coding: utf-8 -*-
print("编码测试: 你好世界 ❤️ 🎉")
print("\\r测试: 原始内容\\r新内容", end="")
print()
print("覆盖测试: 123456\\r999", end="")
print()
print("测试完成")
''')
        
        # 添加任务
        task_id = task_mgr.add_task(
            script_path=f'python "{test_script}"',
            working_dir=temp_dir,
            cron_expression='*/5 * * * *',
            name='快速测试',
            max_runtime=30
        )
        
        print(f"任务已添加，ID: {task_id}")
        
        # 执行任务
        success, message = task_mgr.run_task_now(task_id)
        print(f"任务执行: {success}, {message}")
        
        if success:
            # 等待完成
            time.sleep(3)
            
            # 获取日志
            logs = task_mgr.get_task_logs(task_id, limit=1)
            if logs:
                log_content = task_mgr.get_log_content(logs[0]['id'])
                print("\n日志内容:")
                print("-" * 30)
                print(log_content)
                print("-" * 30)
                
                # 检查结果
                if "你好世界" in log_content and "❤️" in log_content:
                    print("✅ 编码修复成功！")
                else:
                    print("❌ 编码修复失败")
                
                if "新内容" in log_content and "原始内容" not in log_content:
                    print("✅ \\r字符处理成功！")
                else:
                    print("❌ \\r字符处理失败")
                
                if "999" in log_content:
                    print("✅ 覆盖测试成功！")
                else:
                    print("❌ 覆盖测试失败")
            else:
                print("❌ 未找到日志")
        else:
            print("❌ 任务执行失败")
        
        task_mgr.stop()

if __name__ == '__main__':
    main()
    print("\n测试完成！")
