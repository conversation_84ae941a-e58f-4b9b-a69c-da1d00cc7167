@echo off
REM Windows UTF-8 环境启动脚本
REM 确保任务管理器在UTF-8环境下运行

echo ========================================
echo 任务管理器 UTF-8 环境启动脚本
echo ========================================

REM 设置控制台代码页为UTF-8
chcp 65001 >nul

REM 设置Python环境变量强制使用UTF-8
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set PYTHONLEGACYWINDOWSSTDIO=0

REM 设置控制台输出为UTF-8
set PYTHONUNBUFFERED=1

echo 当前代码页: 
chcp

echo Python环境变量:
echo PYTHONIOENCODING=%PYTHONIOENCODING%
echo PYTHONUTF8=%PYTHONUTF8%
echo PYTHONLEGACYWINDOWSSTDIO=%PYTHONLEGACYWINDOWSSTDIO%

echo.
echo 正在启动任务管理器...
echo 如果遇到编码问题，请确保：
echo 1. 控制台字体支持Unicode（如Consolas、微软雅黑等）
echo 2. 脚本文件保存为UTF-8编码
echo 3. Python版本为3.7+
echo.

REM 启动任务管理器
python start.py %*

REM 如果启动失败，显示错误信息
if %errorlevel% neq 0 (
    echo.
    echo 启动失败，错误代码: %errorlevel%
    echo.
    echo 可能的解决方案:
    echo 1. 检查Python是否正确安装
    echo 2. 检查依赖是否已安装: pip install -r requirements.txt
    echo 3. 检查配置文件: .env
    echo 4. 尝试使用管理员权限运行
    echo.
    pause
)
