#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试\r字符处理逻辑
"""
import sys
sys.path.insert(0, '.')

from task_manager import Task_manager


def test_carriage_return_processing():
    """测试\r字符处理逻辑"""
    print("测试\\r字符处理逻辑")
    print("=" * 50)
    
    # 创建一个临时的任务管理器实例来测试方法
    import tempfile
    import os
    
    with tempfile.TemporaryDirectory() as temp_dir:
        os.environ['MONITORING_ENABLED'] = 'False'
        task_mgr = Task_manager(
            db_path=os.path.join(temp_dir, 'test.db'),
            log_dir=os.path.join(temp_dir, 'logs')
        )
        
        # 测试用例
        test_cases = [
            # (输入, 期望输出, 描述)
            ("hello", "hello", "无\\r字符"),
            ("123\r456", "456", "基本覆盖"),
            ("123456\r999", "999456", "部分覆盖"),
            ("hello\rworld", "world", "完全覆盖"),
            ("abc\rde\rfg", "fg", "多次覆盖"),
            ("长字符串\r短", "短字符串", "短内容覆盖长内容"),
            ("短\r很长的字符串", "很长的字符串", "长内容覆盖短内容"),
            ("你好\r世界", "世界", "中文覆盖"),
            ("❤️🎉\r⭐", "⭐", "Unicode覆盖"),
            ("Hello\r你好❤️", "你好❤️", "混合字符覆盖"),
            ("test\r", "", "覆盖为空"),
            ("\rhello", "hello", "开头就是\\r"),
        ]
        
        passed = 0
        total = len(test_cases)
        
        for i, (input_line, expected, description) in enumerate(test_cases, 1):
            result = task_mgr._process_carriage_return(input_line)
            
            if result == expected:
                print(f"✓ 测试 {i:2d}: {description}")
                print(f"    输入: {repr(input_line)}")
                print(f"    期望: {repr(expected)}")
                print(f"    结果: {repr(result)}")
                passed += 1
            else:
                print(f"✗ 测试 {i:2d}: {description}")
                print(f"    输入: {repr(input_line)}")
                print(f"    期望: {repr(expected)}")
                print(f"    结果: {repr(result)}")
            print()
        
        print("=" * 50)
        print(f"测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有\\r字符处理测试通过！")
        else:
            print("❌ 部分测试失败，需要调整逻辑")
        
        task_mgr.stop()
        
        # 清理环境变量
        if 'MONITORING_ENABLED' in os.environ:
            del os.environ['MONITORING_ENABLED']
        
        return passed == total


if __name__ == '__main__':
    success = test_carriage_return_processing()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
