# 使用官方Python运行时作为基础镜像
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p log

# 设置环境变量
ENV FLASK_HOST=0.0.0.0
ENV FLASK_PORT=5001
ENV DB_PATH=/app/data/tasks.db
ENV LOG_DIR=/app/log

# 创建数据目录
RUN mkdir -p /app/data

# 暴露端口
EXPOSE 5001

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5001/api/health || exit 1

# 启动应用
CMD ["python", "start.py"]
