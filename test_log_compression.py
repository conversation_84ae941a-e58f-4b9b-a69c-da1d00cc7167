#!/usr/bin/env python3
"""
测试日志压缩功能的脚本
用于验证日志压缩错误修复是否有效
"""
import os
import tempfile
import time
from pathlib import Path
from log_manager import LogManager


def test_log_compression_with_missing_files():
    """测试当文件不存在时的日志压缩处理"""
    print("测试日志压缩功能...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建日志管理器，启用压缩
        log_manager = LogManager(
            log_dir=temp_dir,
            max_logs=5,
            retention_days=1,
            compress_after_days=0,  # 立即压缩
            compression_enabled=True
        )
        
        print(f"临时日志目录: {temp_dir}")
        
        # 创建一些测试日志文件
        test_files = []
        for i in range(3):
            log_path = log_manager.create_log_file(task_id=i+1)
            test_files.append(log_path)
            
            # 写入一些测试内容
            with open(log_path, 'w') as f:
                f.write(f"测试日志内容 {i+1}\n" * 100)
            
            print(f"创建测试文件: {log_path}")
        
        # 手动删除一个文件，模拟程序删除日志的情况
        deleted_file = test_files[1]
        if deleted_file.exists():
            deleted_file.unlink()
            print(f"手动删除文件: {deleted_file}")
        
        # 执行日志清理，这应该不会报错
        print("执行日志清理...")
        try:
            log_manager.cleanup_logs()
            print("✓ 日志清理成功完成，没有错误")
        except Exception as e:
            print(f"✗ 日志清理失败: {e}")
            return False
        
        # 检查剩余文件
        remaining_files = log_manager.get_log_files()
        print(f"剩余日志文件数量: {len(remaining_files)}")
        
        for log_file in remaining_files:
            print(f"  - {log_file.path.name} (压缩: {log_file.is_compressed})")
        
        log_manager.stop_cleanup_thread()
        return True


def test_log_compression_disabled():
    """测试禁用日志压缩功能"""
    print("\n测试禁用日志压缩功能...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建日志管理器，禁用压缩
        log_manager = LogManager(
            log_dir=temp_dir,
            max_logs=5,
            retention_days=1,
            compress_after_days=0,  # 立即压缩（但被禁用）
            compression_enabled=False  # 禁用压缩
        )
        
        print(f"临时日志目录: {temp_dir}")
        
        # 创建一些测试日志文件
        for i in range(3):
            log_path = log_manager.create_log_file(task_id=i+1)
            
            # 写入一些测试内容
            with open(log_path, 'w') as f:
                f.write(f"测试日志内容 {i+1}\n" * 100)
            
            print(f"创建测试文件: {log_path}")
        
        # 执行日志清理
        print("执行日志清理（压缩已禁用）...")
        try:
            log_manager.cleanup_logs()
            print("✓ 日志清理成功完成")
        except Exception as e:
            print(f"✗ 日志清理失败: {e}")
            return False
        
        # 检查文件，应该都没有被压缩
        remaining_files = log_manager.get_log_files()
        print(f"剩余日志文件数量: {len(remaining_files)}")
        
        compressed_count = sum(1 for f in remaining_files if f.is_compressed)
        print(f"压缩文件数量: {compressed_count} (应该为0)")
        
        if compressed_count == 0:
            print("✓ 压缩功能已正确禁用")
        else:
            print("✗ 压缩功能未正确禁用")
            return False
        
        log_manager.stop_cleanup_thread()
        return True


def test_log_statistics():
    """测试日志统计功能"""
    print("\n测试日志统计功能...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        log_manager = LogManager(
            log_dir=temp_dir,
            max_logs=10,
            compression_enabled=False
        )
        
        # 创建一些测试文件
        for i in range(5):
            log_path = log_manager.create_log_file(task_id=i+1)
            with open(log_path, 'w') as f:
                f.write(f"测试内容 {i+1}\n" * (i+1) * 10)
        
        # 获取统计信息
        try:
            stats = log_manager.get_log_statistics()
            print("✓ 日志统计获取成功")
            print(f"  总文件数: {stats['total_files']}")
            print(f"  总大小: {stats['total_size']} 字节")
            print(f"  压缩文件数: {stats['compressed_files']}")
            print(f"  未压缩文件数: {stats['uncompressed_files']}")
            print(f"  任务统计: {len(stats['task_statistics'])} 个任务")
        except Exception as e:
            print(f"✗ 日志统计失败: {e}")
            return False
        
        log_manager.stop_cleanup_thread()
        return True


def main():
    """主测试函数"""
    print("=" * 60)
    print("日志管理器测试")
    print("=" * 60)
    
    tests = [
        test_log_compression_with_missing_files,
        test_log_compression_disabled,
        test_log_statistics
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print("✓ 测试通过\n")
            else:
                print("✗ 测试失败\n")
        except Exception as e:
            print(f"✗ 测试异常: {e}\n")
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有测试通过！日志压缩问题已修复。")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查。")
        return False


if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
