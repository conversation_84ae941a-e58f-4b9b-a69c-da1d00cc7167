"""
优化的任务调度器模块
提供高效的任务调度和执行管理
"""
import heapq
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from croniter import croniter
from dataclasses import dataclass
from enum import Enum


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"
    TERMINATED = "terminated"


@dataclass
class ScheduledTask:
    """调度任务数据类"""
    task_id: int
    next_run_time: datetime
    task_info: Dict[str, Any]
    cron_expression: str
    
    def __lt__(self, other):
        """用于堆排序的比较方法"""
        return self.next_run_time < other.next_run_time


class TaskScheduler:
    """优化的任务调度器"""
    
    def __init__(self, task_manager, max_concurrent_tasks: int = 10):
        """
        初始化任务调度器
        
        参数:
            task_manager: 任务管理器实例
            max_concurrent_tasks: 最大并发任务数
        """
        self.task_manager = task_manager
        self.max_concurrent_tasks = max_concurrent_tasks
        
        # 调度相关
        self._task_queue: List[ScheduledTask] = []
        self._task_cache: Dict[int, Dict[str, Any]] = {}
        self._running_tasks: Dict[int, Dict[str, Any]] = {}
        
        # 线程控制
        self._stop_event = threading.Event()
        self._update_event = threading.Event()
        self._scheduler_thread: Optional[threading.Thread] = None
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        # 性能统计
        self._stats = {
            'total_scheduled': 0,
            'total_executed': 0,
            'total_failed': 0,
            'avg_execution_time': 0.0
        }
    
    def start(self):
        """启动调度器"""
        if self._scheduler_thread and self._scheduler_thread.is_alive():
            self.logger.warning("调度器已经在运行中")
            return
        
        self.logger.info("启动任务调度器")
        self._stop_event.clear()
        self._scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self._scheduler_thread.start()
        
        # 初始加载任务
        self._reload_all_tasks()
    
    def stop(self):
        """停止调度器"""
        self.logger.info("停止任务调度器")
        self._stop_event.set()
        if self._scheduler_thread:
            self._scheduler_thread.join(timeout=5)
    
    def add_task(self, task_info: Dict[str, Any]):
        """
        添加新任务到调度器
        
        参数:
            task_info: 任务信息字典
        """
        task_id = task_info['id']
        cron_expression = task_info['cron_expression']
        
        try:
            # 计算下次执行时间
            cron = croniter(cron_expression, datetime.now())
            next_run_time = cron.get_next(datetime)
            
            # 创建调度任务
            scheduled_task = ScheduledTask(
                task_id=task_id,
                next_run_time=next_run_time,
                task_info=task_info,
                cron_expression=cron_expression
            )
            
            # 添加到队列和缓存
            heapq.heappush(self._task_queue, scheduled_task)
            self._task_cache[task_id] = task_info
            
            self.logger.info(f"任务 {task_id} 已添加到调度器，下次执行时间: {next_run_time}")
            self._stats['total_scheduled'] += 1
            
            # 通知调度器更新
            self._update_event.set()
            
        except Exception as e:
            self.logger.error(f"添加任务 {task_id} 到调度器失败: {e}")
    
    def update_task(self, task_info: Dict[str, Any]):
        """
        更新任务信息
        
        参数:
            task_info: 更新后的任务信息
        """
        task_id = task_info['id']
        
        # 更新缓存
        self._task_cache[task_id] = task_info
        
        # 如果cron表达式发生变化，需要重新计算执行时间
        old_task = next((t for t in self._task_queue if t.task_id == task_id), None)
        if old_task and old_task.cron_expression != task_info['cron_expression']:
            # 移除旧任务并添加新任务
            self.remove_task(task_id)
            self.add_task(task_info)
        
        self.logger.info(f"任务 {task_id} 信息已更新")
    
    def remove_task(self, task_id: int):
        """
        从调度器中移除任务
        
        参数:
            task_id: 任务ID
        """
        # 从队列中移除（标记为无效）
        for task in self._task_queue:
            if task.task_id == task_id:
                task.task_id = -1  # 标记为无效
        
        # 从缓存中移除
        self._task_cache.pop(task_id, None)
        
        self.logger.info(f"任务 {task_id} 已从调度器中移除")
    
    def get_running_tasks(self) -> Dict[int, Dict[str, Any]]:
        """获取当前运行中的任务"""
        return self._running_tasks.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取调度器统计信息"""
        return {
            **self._stats,
            'queue_size': len(self._task_queue),
            'running_tasks': len(self._running_tasks),
            'cached_tasks': len(self._task_cache)
        }
    
    def _scheduler_loop(self):
        """调度器主循环"""
        self.logger.info("调度器主循环开始")
        
        while not self._stop_event.is_set():
            try:
                # 检查是否需要更新任务
                if self._update_event.is_set():
                    self._update_event.clear()
                
                # 清理无效任务
                self._cleanup_invalid_tasks()
                
                # 检查是否有任务需要执行
                if self._task_queue:
                    next_task = self._task_queue[0]
                    current_time = datetime.now()
                    
                    if (next_task.task_id > 0 and 
                        current_time >= next_task.next_run_time and
                        len(self._running_tasks) < self.max_concurrent_tasks):
                        
                        # 执行任务
                        self._execute_scheduled_task(next_task)
                        
                        # 计算下次执行时间并重新加入队列
                        self._reschedule_task(next_task)
                    
                    # 计算等待时间
                    if self._task_queue:
                        wait_time = min(
                            (self._task_queue[0].next_run_time - current_time).total_seconds(),
                            1.0
                        )
                        wait_time = max(wait_time, 0.1)
                    else:
                        wait_time = 1.0
                else:
                    wait_time = 1.0
                
                # 等待下次检查
                self._stop_event.wait(timeout=wait_time)
                
            except Exception as e:
                self.logger.error(f"调度器循环出错: {e}")
                time.sleep(1)
        
        self.logger.info("调度器主循环结束")
    
    def _execute_scheduled_task(self, scheduled_task: ScheduledTask):
        """
        执行调度的任务
        
        参数:
            scheduled_task: 调度任务对象
        """
        task_id = scheduled_task.task_id
        task_info = scheduled_task.task_info
        
        # 检查任务是否仍然活跃
        if not task_info.get('is_active', True):
            self.logger.info(f"任务 {task_id} 已被禁用，跳过执行")
            return
        
        # 检查任务是否已在运行
        if task_id in self._running_tasks:
            self.logger.warning(f"任务 {task_id} 已在运行中，跳过本次执行")
            return
        
        try:
            # 记录任务开始执行
            self._running_tasks[task_id] = {
                'start_time': datetime.now(),
                'task_info': task_info
            }
            
            # 在新线程中执行任务
            execution_thread = threading.Thread(
                target=self._task_execution_wrapper,
                args=(task_id, task_info),
                daemon=True
            )
            execution_thread.start()
            
            self.logger.info(f"任务 {task_id} 开始执行")
            self._stats['total_executed'] += 1
            
        except Exception as e:
            self.logger.error(f"启动任务 {task_id} 执行失败: {e}")
            self._running_tasks.pop(task_id, None)
            self._stats['total_failed'] += 1
    
    def _task_execution_wrapper(self, task_id: int, task_info: Dict[str, Any]):
        """
        任务执行包装器
        
        参数:
            task_id: 任务ID
            task_info: 任务信息
        """
        start_time = datetime.now()
        try:
            # 调用任务管理器执行任务
            self.task_manager._execute_task(task_info)
            
        except Exception as e:
            self.logger.error(f"任务 {task_id} 执行出错: {e}")
            self._stats['total_failed'] += 1
        finally:
            # 清理运行中任务记录
            self._running_tasks.pop(task_id, None)
            
            # 更新平均执行时间统计
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_avg_execution_time(execution_time)
    
    def _reschedule_task(self, scheduled_task: ScheduledTask):
        """
        重新调度任务
        
        参数:
            scheduled_task: 调度任务对象
        """
        # 移除当前任务
        heapq.heappop(self._task_queue)
        
        # 计算下次执行时间
        try:
            cron = croniter(scheduled_task.cron_expression, scheduled_task.next_run_time)
            next_run_time = cron.get_next(datetime)
            
            # 创建新的调度任务
            new_scheduled_task = ScheduledTask(
                task_id=scheduled_task.task_id,
                next_run_time=next_run_time,
                task_info=scheduled_task.task_info,
                cron_expression=scheduled_task.cron_expression
            )
            
            # 重新加入队列
            heapq.heappush(self._task_queue, new_scheduled_task)
            
        except Exception as e:
            self.logger.error(f"重新调度任务 {scheduled_task.task_id} 失败: {e}")
    
    def _cleanup_invalid_tasks(self):
        """清理无效的任务"""
        # 移除标记为无效的任务
        valid_tasks = [task for task in self._task_queue if task.task_id > 0]
        if len(valid_tasks) != len(self._task_queue):
            self._task_queue = valid_tasks
            heapq.heapify(self._task_queue)
    
    def _reload_all_tasks(self):
        """重新加载所有活跃任务"""
        try:
            # 获取所有活跃任务
            active_tasks = self.task_manager.get_tasks(include_inactive=False)
            
            # 清空当前队列和缓存
            self._task_queue.clear()
            self._task_cache.clear()
            
            # 添加所有活跃任务
            for task in active_tasks:
                self.add_task(task)
            
            self.logger.info(f"重新加载了 {len(active_tasks)} 个活跃任务")
            
        except Exception as e:
            self.logger.error(f"重新加载任务失败: {e}")
    
    def _update_avg_execution_time(self, execution_time: float):
        """更新平均执行时间统计"""
        total_executed = self._stats['total_executed']
        if total_executed > 0:
            current_avg = self._stats['avg_execution_time']
            self._stats['avg_execution_time'] = (
                (current_avg * (total_executed - 1) + execution_time) / total_executed
            )
