from flask import Flask, render_template, jsonify, request
import datetime
from task_manager import Task_manager
import os
import time
from api_utils import APIResponse, handle_api_errors, validate_json_request, log_api_request
from config import config

app = Flask(__name__)
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # 禁用静态文件缓存
app.config['TEMPLATES_AUTO_RELOAD'] = True  # 禁用 Jinja2 的模板缓存
app.config['SECRET_KEY'] = config.SECRET_KEY

# 初始化任务管理器（避免在重载时重复初始化）
task_manager = None
if not os.environ.get('WERKZEUG_RUN_MAIN'):
    try:
        # 验证配置
        config.validate_config()

        # 创建任务管理器实例，使用配置文件中的设置
        task_manager = Task_manager()

        print(f"任务管理器已初始化，数据库路径: {config.DB_PATH}")
        print(f"日志目录: {config.LOG_DIR}")
        print(f"最大日志数: {config.MAX_LOGS}")

    except Exception as e:
        print(f"初始化任务管理器失败: {e}")
        raise


# 路由
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/dashboard')
def dashboard_data():
    # 获取所有任务
    tasks = task_manager.get_tasks()
    running_tasks = [task for task in tasks if task['id'] in task_manager.running_tasks]
    
    # 获取日志
    all_logs = task_manager.get_task_logs(limit=1000)
    # 最近1天的日志
    one_day_ago = datetime.datetime.now() - datetime.timedelta(days=1)
    # 将日志中的start_time字符串转换为datetime对象后再比较
    one_day_logs = [log for log in all_logs if datetime.datetime.strptime(log['start_time'], '%Y-%m-%d %H:%M:%S') >= one_day_ago]
    completed_logs = [log for log in one_day_logs if log['status'] == 'success']
    failed_logs = [log for log in one_day_logs if log['status'] in ['error', 'timeout']]
    
    return jsonify({
        "running": len(running_tasks),
        "completed": len(completed_logs),
        "failed": len(failed_logs)
    })

@app.route('/api/tasks')
def get_tasks():
    tasks = task_manager.get_tasks(include_inactive=True)
    # 为每个任务添加状态信息
    for task in tasks:
        task['status'] = 'running' if task['id'] in task_manager.running_tasks else 'pending'
        # 获取最新的执行记录
        latest_log = task_manager.get_task_logs(task['id'], limit=1)
        if latest_log:
            task['start_time'] = latest_log[0]['start_time']
            task['end_time'] = latest_log[0]['end_time']
            task['duration'] = latest_log[0]['duration']
            if task['status'] != 'running':
                task['status'] = latest_log[0]['status']
        else:
            task['start_time'] = None
            task['end_time'] = None
            task['duration'] = None
    
    return jsonify(tasks)

@app.route('/api/tasks/<int:task_id>/logs')
def get_task_logs(task_id):
    logs = task_manager.get_task_logs(task_id)
    return jsonify(logs)

@app.route('/api/logs/<int:log_id>')
def get_log_content(log_id):
    # 获取日志文件内容
    content = task_manager.get_log_content(log_id)
    return jsonify({"content": content})

# 为日志批量添加任务名称的辅助函数
def _add_task_names_to_logs(logs):
    if not logs:
        return logs
        
    # 获取所有任务并创建id->name的映射
    all_tasks = task_manager.get_tasks(include_inactive=True)
    task_dict = {task['id']: task.get('name', f'任务 {task["id"]}') for task in all_tasks}
    
    # 为每个日志添加任务名称
    for log in logs:
        log['task_name'] = task_dict.get(log['task_id'], f'任务 {log["task_id"]}')
    
    return logs

@app.route('/api/dashboard/running')
def get_running_tasks():
    tasks = task_manager.get_tasks(include_inactive=True)
    running_tasks = [task for task in tasks if task['id'] in task_manager.running_tasks]
    logs = task_manager.get_task_logs(limit=100)
    # 为每个运行中的任务添加最新日志ID
    for task in running_tasks:
        latest_log = next((log for log in logs if log['task_id'] == task['id']), None)
        task['task_id'] = task['id']
        if latest_log:
            task['id'] = latest_log['id']
        else:
            task['id'] = None
    return jsonify(running_tasks)

@app.route('/api/dashboard/completed')
def get_completed_tasks():
    # 获取成功的日志
    all_logs = task_manager.get_task_logs(limit=100)
    completed_logs = [log for log in all_logs if log['status'] == 'success']
    
    # 添加任务名称
    completed_logs = _add_task_names_to_logs(completed_logs)
    
    return jsonify(completed_logs)

@app.route('/api/dashboard/failed')
def get_failed_tasks():
    # 获取失败的日志
    all_logs = task_manager.get_task_logs(limit=100)
    failed_logs = [log for log in all_logs if log['status'] in ['error', 'timeout']]
    
    # 添加任务名称
    failed_logs = _add_task_names_to_logs(failed_logs)
    
    return jsonify(failed_logs)

# 添加任务相关的新路由
@app.route('/api/tasks', methods=['POST'])
@handle_api_errors
@validate_json_request(
    required_fields=['script_path', 'working_dir', 'cron_expression'],
    field_types={
        'name': str,
        'script_path': str,
        'working_dir': str,
        'cron_expression': str,
        'max_runtime': int,
        'retry_on_error': bool,
        'remark': str
    }
)
@log_api_request
def add_task():
    data = request.json

    task_id = task_manager.add_task(
        script_path=data['script_path'],
        working_dir=data['working_dir'],
        cron_expression=data['cron_expression'],
        name=data.get('name', ''),
        max_runtime=data.get('max_runtime', 3600),
        retry_on_error=data.get('retry_on_error', False),
        remark=data.get('remark', '')
    )

    # 添加默认邮件通知（如果配置了SMTP）
    smtp_config = config.get_smtp_config()
    if smtp_config and smtp_config.get('recipients'):
        task_manager.add_notification(task_id, 'email', {'recipients': smtp_config['recipients']})

    return APIResponse.success(data={"task_id": task_id}, message="任务添加成功")

@app.route('/api/tasks/<int:task_id>', methods=['PUT'])
def update_task(task_id):
    data = request.json
    try:
        success = task_manager.edit_task(
            task_id=task_id,
            script_path=data.get('script_path'),
            working_dir=data.get('working_dir'),
            cron_expression=data.get('cron_expression'),
            name=data.get('name'),
            max_runtime=data.get('max_runtime'),
            retry_on_error=data.get('retry_on_error'),
            remark=data.get('remark'),
            is_active=data.get('is_active')
        )
        task_manager.add_notification(task_id, 'email', {'recipients': ['<EMAIL>']})
        return jsonify({"success": success})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 400

@app.route('/api/tasks/<int:task_id>', methods=['DELETE'])
def delete_task(task_id):
    try:
        task_manager.remove_task(task_id)
        return jsonify({"success": True})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 400

@app.route('/api/tasks/<int:task_id>/run', methods=['POST'])
def run_task(task_id):
    try:
        # 获取任务信息，即使是禁用状态也可以运行
        task = task_manager.get_task(task_id)
        if not task:
            return jsonify({"success": False, "error": "任务不存在"}), 404
            
        # 即使任务被禁用也允许手动运行
        success, message = task_manager.run_task_now(task_id)
        
        # 如果任务被禁用，添加提示信息
        if task.get('is_active') == 0 and success:
            message = "已禁用的任务手动执行成功"
            
        return jsonify({"success": success, "message": message})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 400

@app.route('/api/tasks/<int:task_id>/terminate', methods=['POST'])
def terminate_task(task_id):
    """
    终止正在运行的任务
    """
    try:
        # 获取任务信息
        task = task_manager.get_task(task_id)
        if not task:
            return jsonify({"success": False, "error": "任务不存在"}), 404
        
        # 检查任务是否在运行中
        if task_id not in task_manager.running_tasks:
            return jsonify({"success": False, "error": "任务未在运行中"}), 400
        
        # 调用任务管理器的终止任务方法
        success, message = task_manager.terminate_task(task_id)
        
        # 确保前端重新加载任务列表
        if success:
            # 强制等待一段时间确保数据库操作完成
            time.sleep(0.5)
        
        return jsonify({"success": success, "message": message})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

# 新增健康检查和监控API
@app.route('/api/health')
@handle_api_errors
def health_check():
    """系统健康检查"""
    if task_manager and hasattr(task_manager, 'monitor') and task_manager.monitor:
        health_status = task_manager.monitor.health_checker.perform_health_check()
        return APIResponse.success(data=health_status, message="健康检查完成")
    else:
        return APIResponse.error(message="监控系统未启用", code=503)

@app.route('/api/metrics')
@handle_api_errors
def get_metrics():
    """获取系统指标"""
    if task_manager and hasattr(task_manager, 'monitor') and task_manager.monitor:
        current_status = task_manager.monitor.get_current_status()
        return APIResponse.success(data=current_status, message="指标获取成功")
    else:
        return APIResponse.error(message="监控系统未启用", code=503)

@app.route('/api/metrics/history')
@handle_api_errors
def get_metrics_history():
    """获取历史指标数据"""
    hours = request.args.get('hours', 24, type=int)
    hours = min(hours, 168)  # 最多7天

    if task_manager and hasattr(task_manager, 'monitor') and task_manager.monitor:
        history = task_manager.monitor.get_metrics_history(hours=hours)
        return APIResponse.success(data=history, message="历史指标获取成功")
    else:
        return APIResponse.error(message="监控系统未启用", code=503)

@app.route('/api/logs/statistics')
@handle_api_errors
def get_log_statistics():
    """获取日志统计信息"""
    if task_manager and hasattr(task_manager, 'log_manager'):
        stats = task_manager.log_manager.get_log_statistics()
        return APIResponse.success(data=stats, message="日志统计获取成功")
    else:
        return APIResponse.error(message="日志管理器未启用", code=503)

if __name__ == '__main__':
    try:
        app.run(
            host=config.FLASK_HOST,
            port=config.FLASK_PORT,
            debug=config.FLASK_DEBUG
        )
    finally:
        # 确保在应用关闭时清理资源
        if task_manager:
            task_manager.stop()