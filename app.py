from flask import Flask, render_template, jsonify, request
import datetime
from task_manager import Task_manager
import os
import sqlite3
import time

app = Flask(__name__)
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # 禁用静态文件缓存 这个配置只影响静态文件（如 CSS、JavaScript、图片等）的缓存
app.config['TEMPLATES_AUTO_RELOAD'] = True # 禁用 Jinja2 的模板缓存 .不是高并发可以开启

# SMTP配置
smtp_config = {
    'host': 'smtp.feishu.cn',
    'port': 465,
    'username': '<EMAIL>',
    'password': 'NvmTv3B9xricYjBn',
    'sender': '<EMAIL>',
    'use_ssl': True,
    'recipients': ['<EMAIL>']
}
task_manager = None
if not os.environ.get('WERKZEUG_RUN_MAIN'):
    task_manager = Task_manager(
        db_path='tasks.db',
        log_dir='log',
        max_logs=100,
        smtp_config=smtp_config
    )


# 路由
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/dashboard')
def dashboard_data():
    # 获取所有任务
    tasks = task_manager.get_tasks()
    running_tasks = [task for task in tasks if task['id'] in task_manager.running_tasks]
    
    # 获取日志
    all_logs = task_manager.get_task_logs(limit=1000)
    # 最近1天的日志
    one_day_ago = datetime.datetime.now() - datetime.timedelta(days=1)
    # 将日志中的start_time字符串转换为datetime对象后再比较
    one_day_logs = [log for log in all_logs if datetime.datetime.strptime(log['start_time'], '%Y-%m-%d %H:%M:%S') >= one_day_ago]
    completed_logs = [log for log in one_day_logs if log['status'] == 'success']
    failed_logs = [log for log in one_day_logs if log['status'] in ['error', 'timeout']]
    
    return jsonify({
        "running": len(running_tasks),
        "completed": len(completed_logs),
        "failed": len(failed_logs)
    })

@app.route('/api/tasks')
def get_tasks():
    tasks = task_manager.get_tasks(include_inactive=True)
    # 为每个任务添加状态信息
    for task in tasks:
        task['status'] = 'running' if task['id'] in task_manager.running_tasks else 'pending'
        # 获取最新的执行记录
        latest_log = task_manager.get_task_logs(task['id'], limit=1)
        if latest_log:
            task['start_time'] = latest_log[0]['start_time']
            task['end_time'] = latest_log[0]['end_time']
            task['duration'] = latest_log[0]['duration']
            if task['status'] != 'running':
                task['status'] = latest_log[0]['status']
        else:
            task['start_time'] = None
            task['end_time'] = None
            task['duration'] = None
    
    return jsonify(tasks)

@app.route('/api/tasks/<int:task_id>/logs')
def get_task_logs(task_id):
    logs = task_manager.get_task_logs(task_id)
    return jsonify(logs)

@app.route('/api/logs/<int:log_id>')
def get_log_content(log_id):
    # 获取日志文件内容
    content = task_manager.get_log_content(log_id)
    return jsonify({"content": content})

# 为日志批量添加任务名称的辅助函数
def _add_task_names_to_logs(logs):
    if not logs:
        return logs
        
    # 获取所有任务并创建id->name的映射
    all_tasks = task_manager.get_tasks(include_inactive=True)
    task_dict = {task['id']: task.get('name', f'任务 {task["id"]}') for task in all_tasks}
    
    # 为每个日志添加任务名称
    for log in logs:
        log['task_name'] = task_dict.get(log['task_id'], f'任务 {log["task_id"]}')
    
    return logs

@app.route('/api/dashboard/running')
def get_running_tasks():
    tasks = task_manager.get_tasks(include_inactive=True)
    running_tasks = [task for task in tasks if task['id'] in task_manager.running_tasks]
    logs = task_manager.get_task_logs(limit=100)
    # 为每个运行中的任务添加最新日志ID
    for task in running_tasks:
        latest_log = next((log for log in logs if log['task_id'] == task['id']), None)
        task['task_id'] = task['id']
        if latest_log:
            task['id'] = latest_log['id']
        else:
            task['id'] = None
    return jsonify(running_tasks)

@app.route('/api/dashboard/completed')
def get_completed_tasks():
    # 获取成功的日志
    all_logs = task_manager.get_task_logs(limit=100)
    completed_logs = [log for log in all_logs if log['status'] == 'success']
    
    # 添加任务名称
    completed_logs = _add_task_names_to_logs(completed_logs)
    
    return jsonify(completed_logs)

@app.route('/api/dashboard/failed')
def get_failed_tasks():
    # 获取失败的日志
    all_logs = task_manager.get_task_logs(limit=100)
    failed_logs = [log for log in all_logs if log['status'] in ['error', 'timeout']]
    
    # 添加任务名称
    failed_logs = _add_task_names_to_logs(failed_logs)
    
    return jsonify(failed_logs)

# 添加任务相关的新路由
@app.route('/api/tasks', methods=['POST'])
def add_task():
    data = request.json
    try:
        task_id = task_manager.add_task(
            script_path=data['script_path'],
            working_dir=data['working_dir'],
            cron_expression=data['cron_expression'],
            name=data.get('name', ''),
            max_runtime=data.get('max_runtime', 3600),
            retry_on_error=data.get('retry_on_error', False),
            remark=data.get('remark', '')
        )
        task_manager.add_notification(task_id, 'email', {'recipients': ['<EMAIL>']})
        return jsonify({"success": True, "task_id": task_id})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 400

@app.route('/api/tasks/<int:task_id>', methods=['PUT'])
def update_task(task_id):
    data = request.json
    try:
        success = task_manager.edit_task(
            task_id=task_id,
            script_path=data.get('script_path'),
            working_dir=data.get('working_dir'),
            cron_expression=data.get('cron_expression'),
            name=data.get('name'),
            max_runtime=data.get('max_runtime'),
            retry_on_error=data.get('retry_on_error'),
            remark=data.get('remark'),
            is_active=data.get('is_active')
        )
        task_manager.add_notification(task_id, 'email', {'recipients': ['<EMAIL>']})
        return jsonify({"success": success})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 400

@app.route('/api/tasks/<int:task_id>', methods=['DELETE'])
def delete_task(task_id):
    try:
        task_manager.remove_task(task_id)
        return jsonify({"success": True})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 400

@app.route('/api/tasks/<int:task_id>/run', methods=['POST'])
def run_task(task_id):
    try:
        # 获取任务信息，即使是禁用状态也可以运行
        task = task_manager.get_task(task_id)
        if not task:
            return jsonify({"success": False, "error": "任务不存在"}), 404
            
        # 即使任务被禁用也允许手动运行
        success, message = task_manager.run_task_now(task_id)
        
        # 如果任务被禁用，添加提示信息
        if task.get('is_active') == 0 and success:
            message = "已禁用的任务手动执行成功"
            
        return jsonify({"success": success, "message": message})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 400

@app.route('/api/tasks/<int:task_id>/terminate', methods=['POST'])
def terminate_task(task_id):
    """
    终止正在运行的任务
    """
    try:
        # 获取任务信息
        task = task_manager.get_task(task_id)
        if not task:
            return jsonify({"success": False, "error": "任务不存在"}), 404
        
        # 检查任务是否在运行中
        if task_id not in task_manager.running_tasks:
            return jsonify({"success": False, "error": "任务未在运行中"}), 400
        
        # 调用任务管理器的终止任务方法
        success, message = task_manager.terminate_task(task_id)
        
        # 确保前端重新加载任务列表
        if success:
            # 强制等待一段时间确保数据库操作完成
            time.sleep(0.5)
        
        return jsonify({"success": success, "message": message})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

if __name__ == '__main__':
    app.run(port=5001) 