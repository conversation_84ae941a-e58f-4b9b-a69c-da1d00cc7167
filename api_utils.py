"""
API工具模块
提供统一的API响应格式、错误处理、验证等功能
"""
import functools
import logging
import traceback
from datetime import datetime
from typing import Any, Dict, Optional, Callable, Union
from flask import jsonify, request
from werkzeug.exceptions import HTTPException


class APIResponse:
    """API响应类"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功", code: int = 200) -> Dict[str, Any]:
        """
        创建成功响应
        
        参数:
            data: 响应数据
            message: 响应消息
            code: HTTP状态码
            
        返回:
            响应字典
        """
        response = {
            "success": True,
            "code": code,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }
        return jsonify(response), code
    
    @staticmethod
    def error(message: str = "操作失败", code: int = 400, 
              error_type: str = "BadRequest", details: Any = None) -> Dict[str, Any]:
        """
        创建错误响应
        
        参数:
            message: 错误消息
            code: HTTP状态码
            error_type: 错误类型
            details: 错误详情
            
        返回:
            响应字典
        """
        response = {
            "success": False,
            "code": code,
            "message": message,
            "error_type": error_type,
            "timestamp": datetime.now().isoformat()
        }
        
        if details:
            response["details"] = details
            
        return jsonify(response), code
    
    @staticmethod
    def paginated(data: list, page: int, per_page: int, total: int, 
                  message: str = "查询成功") -> Dict[str, Any]:
        """
        创建分页响应
        
        参数:
            data: 数据列表
            page: 当前页码
            per_page: 每页数量
            total: 总记录数
            message: 响应消息
            
        返回:
            响应字典
        """
        total_pages = (total + per_page - 1) // per_page
        
        pagination_info = {
            "page": page,
            "per_page": per_page,
            "total": total,
            "total_pages": total_pages,
            "has_next": page < total_pages,
            "has_prev": page > 1
        }
        
        response_data = {
            "items": data,
            "pagination": pagination_info
        }
        
        return APIResponse.success(data=response_data, message=message)


class APIValidator:
    """API参数验证器"""
    
    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: list) -> Optional[str]:
        """
        验证必需字段
        
        参数:
            data: 数据字典
            required_fields: 必需字段列表
            
        返回:
            错误消息，如果验证通过则返回None
        """
        missing_fields = [field for field in required_fields if field not in data or data[field] is None]
        
        if missing_fields:
            return f"缺少必需字段: {', '.join(missing_fields)}"
        
        return None
    
    @staticmethod
    def validate_field_types(data: Dict[str, Any], field_types: Dict[str, type]) -> Optional[str]:
        """
        验证字段类型
        
        参数:
            data: 数据字典
            field_types: 字段类型映射
            
        返回:
            错误消息，如果验证通过则返回None
        """
        for field, expected_type in field_types.items():
            if field in data and data[field] is not None:
                if not isinstance(data[field], expected_type):
                    return f"字段 {field} 类型错误，期望 {expected_type.__name__}"
        
        return None
    
    @staticmethod
    def validate_cron_expression(cron_expr: str) -> Optional[str]:
        """
        验证cron表达式
        
        参数:
            cron_expr: cron表达式
            
        返回:
            错误消息，如果验证通过则返回None
        """
        try:
            from croniter import croniter
            croniter(cron_expr)
            return None
        except ValueError as e:
            return f"无效的cron表达式: {str(e)}"
    
    @staticmethod
    def validate_task_data(data: Dict[str, Any], is_update: bool = False) -> Optional[str]:
        """
        验证任务数据
        
        参数:
            data: 任务数据
            is_update: 是否为更新操作
            
        返回:
            错误消息，如果验证通过则返回None
        """
        if not is_update:
            # 创建任务时的必需字段
            required_fields = ['script_path', 'working_dir', 'cron_expression']
            error = APIValidator.validate_required_fields(data, required_fields)
            if error:
                return error
        
        # 字段类型验证
        field_types = {
            'name': str,
            'script_path': str,
            'working_dir': str,
            'cron_expression': str,
            'max_runtime': int,
            'retry_on_error': bool,
            'remark': str,
            'is_active': bool
        }
        
        error = APIValidator.validate_field_types(data, field_types)
        if error:
            return error
        
        # cron表达式验证
        if 'cron_expression' in data:
            error = APIValidator.validate_cron_expression(data['cron_expression'])
            if error:
                return error
        
        # 数值范围验证
        if 'max_runtime' in data and data['max_runtime'] <= 0:
            return "max_runtime 必须大于0"
        
        return None


def handle_api_errors(f: Callable) -> Callable:
    """
    API错误处理装饰器
    
    参数:
        f: 被装饰的函数
        
    返回:
        装饰后的函数
    """
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except HTTPException as e:
            # Flask HTTP异常
            return APIResponse.error(
                message=e.description,
                code=e.code,
                error_type=e.__class__.__name__
            )
        except ValueError as e:
            # 参数验证错误
            return APIResponse.error(
                message=str(e),
                code=400,
                error_type="ValidationError"
            )
        except FileNotFoundError as e:
            # 文件不存在错误
            return APIResponse.error(
                message=f"文件不存在: {str(e)}",
                code=404,
                error_type="FileNotFoundError"
            )
        except PermissionError as e:
            # 权限错误
            return APIResponse.error(
                message=f"权限不足: {str(e)}",
                code=403,
                error_type="PermissionError"
            )
        except Exception as e:
            # 其他未知错误
            logger = logging.getLogger(__name__)
            logger.error(f"API调用出错: {str(e)}\n{traceback.format_exc()}")
            
            return APIResponse.error(
                message="服务器内部错误",
                code=500,
                error_type="InternalServerError",
                details=str(e) if logger.level <= logging.DEBUG else None
            )
    
    return wrapper


def validate_json_request(required_fields: list = None, 
                         field_types: Dict[str, type] = None) -> Callable:
    """
    JSON请求验证装饰器
    
    参数:
        required_fields: 必需字段列表
        field_types: 字段类型映射
        
    返回:
        装饰器函数
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def wrapper(*args, **kwargs):
            # 检查Content-Type
            if not request.is_json:
                return APIResponse.error(
                    message="请求必须是JSON格式",
                    code=400,
                    error_type="InvalidContentType"
                )
            
            data = request.get_json()
            if data is None:
                return APIResponse.error(
                    message="无效的JSON数据",
                    code=400,
                    error_type="InvalidJSON"
                )
            
            # 验证必需字段
            if required_fields:
                error = APIValidator.validate_required_fields(data, required_fields)
                if error:
                    return APIResponse.error(
                        message=error,
                        code=400,
                        error_type="ValidationError"
                    )
            
            # 验证字段类型
            if field_types:
                error = APIValidator.validate_field_types(data, field_types)
                if error:
                    return APIResponse.error(
                        message=error,
                        code=400,
                        error_type="ValidationError"
                    )
            
            return f(*args, **kwargs)
        
        return wrapper
    return decorator


def get_pagination_params() -> Dict[str, int]:
    """
    获取分页参数
    
    返回:
        分页参数字典
    """
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # 限制每页最大数量
    per_page = min(per_page, 100)
    page = max(page, 1)
    
    return {
        'page': page,
        'per_page': per_page,
        'offset': (page - 1) * per_page
    }


def log_api_request(f: Callable) -> Callable:
    """
    API请求日志装饰器
    
    参数:
        f: 被装饰的函数
        
    返回:
        装饰后的函数
    """
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(__name__)
        
        # 记录请求信息
        logger.info(f"API请求: {request.method} {request.path}")
        logger.debug(f"请求参数: {request.args}")
        
        if request.is_json:
            logger.debug(f"请求体: {request.get_json()}")
        
        start_time = datetime.now()
        
        try:
            result = f(*args, **kwargs)
            
            # 记录响应时间
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"API响应: {request.method} {request.path} - {duration:.3f}s")
            
            return result
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"API错误: {request.method} {request.path} - {duration:.3f}s - {str(e)}")
            raise
    
    return wrapper


class RateLimiter:
    """简单的速率限制器"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        """
        初始化速率限制器
        
        参数:
            max_requests: 时间窗口内最大请求数
            window_seconds: 时间窗口大小（秒）
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}
    
    def is_allowed(self, client_id: str) -> bool:
        """
        检查是否允许请求
        
        参数:
            client_id: 客户端标识
            
        返回:
            是否允许请求
        """
        now = datetime.now()
        
        # 清理过期记录
        self._cleanup_expired_requests(now)
        
        # 检查当前客户端的请求数
        if client_id not in self.requests:
            self.requests[client_id] = []
        
        client_requests = self.requests[client_id]
        
        # 过滤时间窗口内的请求
        window_start = now.timestamp() - self.window_seconds
        recent_requests = [req_time for req_time in client_requests if req_time > window_start]
        
        if len(recent_requests) >= self.max_requests:
            return False
        
        # 记录当前请求
        recent_requests.append(now.timestamp())
        self.requests[client_id] = recent_requests
        
        return True
    
    def _cleanup_expired_requests(self, now: datetime):
        """清理过期的请求记录"""
        window_start = now.timestamp() - self.window_seconds
        
        for client_id in list(self.requests.keys()):
            self.requests[client_id] = [
                req_time for req_time in self.requests[client_id]
                if req_time > window_start
            ]
            
            # 如果没有最近的请求，删除客户端记录
            if not self.requests[client_id]:
                del self.requests[client_id]


def rate_limit(max_requests: int = 100, window_seconds: int = 60) -> Callable:
    """
    速率限制装饰器
    
    参数:
        max_requests: 时间窗口内最大请求数
        window_seconds: 时间窗口大小（秒）
        
    返回:
        装饰器函数
    """
    limiter = RateLimiter(max_requests, window_seconds)
    
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def wrapper(*args, **kwargs):
            # 使用IP地址作为客户端标识
            client_id = request.remote_addr
            
            if not limiter.is_allowed(client_id):
                return APIResponse.error(
                    message="请求过于频繁，请稍后再试",
                    code=429,
                    error_type="RateLimitExceeded"
                )
            
            return f(*args, **kwargs)
        
        return wrapper
    return decorator
