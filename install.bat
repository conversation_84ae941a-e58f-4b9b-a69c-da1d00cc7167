@echo off
REM 任务管理器 v2.0 Windows安装脚本

echo ==================================
echo 任务管理器 v2.0 安装脚本
echo ==================================

REM 检查Python
echo 检查Python版本...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python。请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo 找到Python版本: %PYTHON_VERSION%

REM 检查Python版本是否满足要求
python -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"
if %errorlevel% neq 0 (
    echo 错误: Python版本过低，需要3.8或更高版本
    pause
    exit /b 1
)
echo ✓ Python版本满足要求

REM 检查pip
echo 检查pip...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到pip。请确保pip已正确安装
    pause
    exit /b 1
)
echo ✓ 找到pip

REM 安装依赖
echo 安装Python依赖...
if not exist requirements.txt (
    echo 错误: 未找到requirements.txt文件
    pause
    exit /b 1
)

pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 错误: 依赖安装失败
    pause
    exit /b 1
)
echo ✓ 依赖安装完成

REM 创建配置文件
echo 设置配置文件...
if not exist .env (
    if exist .env.example (
        copy .env.example .env >nul
        echo ✓ 已创建配置文件 .env
        echo 请编辑 .env 文件以配置您的设置
    ) else (
        echo 警告: 未找到配置模板文件 .env.example
    )
) else (
    echo ✓ 配置文件已存在
)

REM 创建必要目录
echo 创建必要目录...
if not exist log mkdir log
if not exist data mkdir data
echo ✓ 目录创建完成

REM 初始化数据库
echo 初始化数据库...
python start.py --init
if %errorlevel% neq 0 (
    echo 错误: 数据库初始化失败
    pause
    exit /b 1
)
echo ✓ 数据库初始化完成

REM 运行测试
echo 运行测试...
pip show pytest >nul 2>&1
if %errorlevel% equ 0 (
    python start.py --test
    echo ✓ 测试完成
) else (
    echo 跳过测试 (pytest未安装)
)

REM 显示完成信息
echo.
echo ==================================
echo 安装完成!
echo ==================================
echo.
echo 启动方式:
echo   开发模式: python start.py
echo   生产模式: python start.py --host 0.0.0.0
echo   调试模式: python start.py --debug
echo.
echo 访问地址: http://localhost:5001
echo.
echo 更多信息请查看 README.md
echo.

REM 询问是否立即启动
set /p choice="是否立即启动任务管理器? (y/n): "
if /i "%choice%"=="y" (
    echo 正在启动任务管理器...
    python start.py
) else (
    echo 安装完成。您可以稍后运行 'python start.py' 来启动服务。
)

pause
