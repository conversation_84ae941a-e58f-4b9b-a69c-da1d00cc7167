"""
基础功能测试
"""
import pytest
import tempfile
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from task_manager import Task_manager
from database import DatabasePool, DatabaseManager
from log_manager import LogManager
from config import Config


class TestConfig:
    """配置测试"""
    
    def test_config_creation(self):
        """测试配置创建"""
        config = Config()
        assert hasattr(config, 'DB_PATH')
        assert hasattr(config, 'LOG_DIR')
        assert hasattr(config, 'MAX_LOGS')
    
    def test_config_validation(self):
        """测试配置验证"""
        config = Config()
        # 应该不抛出异常
        assert config.validate_config() == True


class TestDatabase:
    """数据库测试"""
    
    @pytest.fixture
    def temp_db(self):
        """临时数据库fixture"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        yield db_path
        
        # 清理
        if os.path.exists(db_path):
            os.unlink(db_path)
    
    def test_database_pool_creation(self, temp_db):
        """测试数据库连接池创建"""
        pool = DatabasePool(temp_db, max_connections=5)
        assert pool.max_connections == 5
        assert pool._created_connections >= 0
        pool.close_all()
    
    def test_database_connection(self, temp_db):
        """测试数据库连接"""
        pool = DatabasePool(temp_db)
        
        with pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            assert result[0] == 1
        
        pool.close_all()
    
    def test_database_manager(self, temp_db):
        """测试数据库管理器"""
        pool = DatabasePool(temp_db)
        manager = DatabaseManager(pool)
        
        # 测试查询
        results = manager.execute_query("SELECT 1 as test")
        assert len(results) == 1
        assert results[0]['test'] == 1
        
        pool.close_all()


class TestLogManager:
    """日志管理器测试"""
    
    @pytest.fixture
    def temp_log_dir(self):
        """临时日志目录fixture"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    def test_log_manager_creation(self, temp_log_dir):
        """测试日志管理器创建"""
        log_manager = LogManager(temp_log_dir, max_logs=10)
        assert log_manager.max_logs == 10
        assert log_manager.log_dir == Path(temp_log_dir)
        log_manager.stop_cleanup_thread()
    
    def test_log_file_creation(self, temp_log_dir):
        """测试日志文件创建"""
        log_manager = LogManager(temp_log_dir)
        
        log_path = log_manager.create_log_file(task_id=1)
        assert log_path.exists()
        assert log_path.name.startswith('1_')
        assert log_path.suffix == '.log'
        
        log_manager.stop_cleanup_thread()
    
    def test_log_file_listing(self, temp_log_dir):
        """测试日志文件列表"""
        log_manager = LogManager(temp_log_dir)
        
        # 创建几个日志文件
        log_manager.create_log_file(task_id=1)
        log_manager.create_log_file(task_id=2)
        
        log_files = log_manager.get_log_files()
        assert len(log_files) == 2
        
        # 测试按任务ID过滤
        task1_logs = log_manager.get_log_files(task_id=1)
        assert len(task1_logs) == 1
        assert task1_logs[0].task_id == 1
        
        log_manager.stop_cleanup_thread()


class TestTaskManager:
    """任务管理器测试"""
    
    @pytest.fixture
    def temp_task_manager(self):
        """临时任务管理器fixture"""
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = os.path.join(temp_dir, 'test.db')
            log_dir = os.path.join(temp_dir, 'logs')
            
            # 创建任务管理器，禁用监控以避免测试中的复杂性
            os.environ['MONITORING_ENABLED'] = 'False'
            
            task_manager = Task_manager(
                db_path=db_path,
                log_dir=log_dir,
                max_logs=10
            )
            
            yield task_manager
            
            # 清理
            task_manager.stop()
            
            # 恢复环境变量
            if 'MONITORING_ENABLED' in os.environ:
                del os.environ['MONITORING_ENABLED']
    
    def test_task_manager_creation(self, temp_task_manager):
        """测试任务管理器创建"""
        assert temp_task_manager is not None
        assert hasattr(temp_task_manager, 'db_manager')
        assert hasattr(temp_task_manager, 'log_manager')
        assert hasattr(temp_task_manager, 'scheduler')
    
    def test_add_task(self, temp_task_manager):
        """测试添加任务"""
        task_id = temp_task_manager.add_task(
            script_path='echo "test"',
            working_dir='/tmp',
            cron_expression='*/5 * * * *',
            name='测试任务',
            max_runtime=60,
            retry_on_error=False,
            remark='这是一个测试任务'
        )
        
        assert task_id is not None
        assert isinstance(task_id, int)
        assert task_id > 0
    
    def test_get_tasks(self, temp_task_manager):
        """测试获取任务列表"""
        # 添加一个任务
        task_id = temp_task_manager.add_task(
            script_path='echo "test"',
            working_dir='/tmp',
            cron_expression='*/5 * * * *',
            name='测试任务'
        )
        
        # 获取任务列表
        tasks = temp_task_manager.get_tasks()
        assert len(tasks) == 1
        assert tasks[0]['id'] == task_id
        assert tasks[0]['name'] == '测试任务'
    
    def test_get_single_task(self, temp_task_manager):
        """测试获取单个任务"""
        # 添加一个任务
        task_id = temp_task_manager.add_task(
            script_path='echo "test"',
            working_dir='/tmp',
            cron_expression='*/5 * * * *',
            name='测试任务'
        )
        
        # 获取单个任务
        task = temp_task_manager.get_task(task_id)
        assert task is not None
        assert task['id'] == task_id
        assert task['name'] == '测试任务'
        
        # 测试不存在的任务
        non_existent_task = temp_task_manager.get_task(99999)
        assert non_existent_task is None
    
    def test_invalid_cron_expression(self, temp_task_manager):
        """测试无效的cron表达式"""
        with pytest.raises(ValueError, match="无效的crontab表达式"):
            temp_task_manager.add_task(
                script_path='echo "test"',
                working_dir='/tmp',
                cron_expression='invalid cron',
                name='无效任务'
            )


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
