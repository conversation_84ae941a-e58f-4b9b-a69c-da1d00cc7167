#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证编码和\r字符修复的测试脚本
"""
import os
import sys
import tempfile
import time
from pathlib import Path


def test_task_manager_fixes():
    """测试任务管理器的修复"""
    print("=" * 60)
    print("测试任务管理器编码和\\r字符修复")
    print("=" * 60)
    
    try:
        # 导入任务管理器
        sys.path.insert(0, '.')
        from task_manager import Task_manager
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = os.path.join(temp_dir, 'test.db')
            log_dir = os.path.join(temp_dir, 'logs')
            
            # 禁用监控
            os.environ['MONITORING_ENABLED'] = 'False'
            
            # 创建任务管理器
            task_mgr = Task_manager(
                db_path=db_path,
                log_dir=log_dir,
                max_logs=10
            )
            
            print("✓ 任务管理器创建成功")
            
            # 创建测试脚本
            test_script = os.path.join(temp_dir, 'test_fixes.py')
            with open(test_script, 'w', encoding='utf-8') as f:
                f.write('''# -*- coding: utf-8 -*-
import time

print("=== 编码测试 ===")
print("中文: 你好世界")
print("Unicode: ❤️ 🎉 🚀 ⭐ 🌍")

print("=== \\\\r字符测试 ===")
print("基本覆盖测试:")
print("原始内容\\r新内容", end="")
print()

print("多次覆盖测试:")
print("第一次\\r第二次\\r最终内容", end="")
print()

print("部分覆盖测试:")
print("123456\\r999", end="")
print()

print("中文覆盖测试:")
print("你好世界\\r再见", end="")
print()

print("Unicode覆盖测试:")
print("❤️🎉🚀\\r⭐", end="")
print()

print("进度条测试:")
for i in range(3):
    progress = "█" * (i+1) + "░" * (2-i)
    print(f"进度: {progress} {(i+1)*33}%\\r", end="")
    time.sleep(0.2)
print()

print("=== 测试完成 ===")
''')
            
            # 添加测试任务
            task_id = task_mgr.add_task(
                script_path=f'python "{test_script}"',
                working_dir=temp_dir,
                cron_expression='*/5 * * * *',
                name='修复验证测试',
                max_runtime=30
            )
            
            print(f"✓ 测试任务已添加，ID: {task_id}")
            
            # 立即运行任务
            success, message = task_mgr.run_task_now(task_id)
            print(f"任务执行结果: {success}, 消息: {message}")
            
            if success:
                # 等待任务完成
                print("等待任务完成...")
                time.sleep(5)
                
                # 获取日志
                logs = task_mgr.get_task_logs(task_id, limit=1)
                if logs:
                    log_content = task_mgr.get_log_content(logs[0]['id'])
                    print("\n" + "=" * 40)
                    print("任务日志内容:")
                    print("=" * 40)
                    print(log_content)
                    print("=" * 40)
                    
                    # 验证修复效果
                    success_checks = []
                    
                    # 检查中文字符
                    if "你好世界" in log_content:
                        print("✓ 中文字符显示正常")
                        success_checks.append(True)
                    else:
                        print("✗ 中文字符显示异常")
                        success_checks.append(False)
                    
                    # 检查Unicode字符
                    if "❤️" in log_content and "🎉" in log_content:
                        print("✓ Unicode字符显示正常")
                        success_checks.append(True)
                    else:
                        print("✗ Unicode字符显示异常")
                        success_checks.append(False)
                    
                    # 检查\r字符处理
                    # 应该看到"新内容"而不是"原始内容"
                    if "新内容" in log_content and "原始内容" not in log_content:
                        print("✓ \\r字符覆盖处理正常")
                        success_checks.append(True)
                    else:
                        print("✗ \\r字符覆盖处理异常")
                        print("  检查日志中是否正确处理了\\r字符覆盖")
                        success_checks.append(False)
                    
                    # 检查最终内容覆盖
                    if "最终内容" in log_content and "第一次" not in log_content and "第二次" not in log_content:
                        print("✓ 多次\\r覆盖处理正常")
                        success_checks.append(True)
                    else:
                        print("✗ 多次\\r覆盖处理异常")
                        success_checks.append(False)
                    
                    # 总结
                    passed = sum(success_checks)
                    total = len(success_checks)
                    
                    print(f"\n修复验证结果: {passed}/{total} 项通过")
                    
                    if passed == total:
                        print("🎉 所有修复验证通过！")
                        return True
                    else:
                        print("❌ 部分修复验证失败")
                        return False
                else:
                    print("✗ 未找到任务日志")
                    return False
            else:
                print("✗ 任务执行失败")
                return False
                
    except Exception as e:
        print(f"✗ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理环境变量
        if 'MONITORING_ENABLED' in os.environ:
            del os.environ['MONITORING_ENABLED']


def show_environment_info():
    """显示环境信息"""
    print("环境信息:")
    print(f"Python版本: {sys.version}")
    print(f"平台: {sys.platform}")
    print(f"默认编码: {sys.getdefaultencoding()}")
    print(f"PYTHONIOENCODING: {os.environ.get('PYTHONIOENCODING', '未设置')}")
    print(f"PYTHONUTF8: {os.environ.get('PYTHONUTF8', '未设置')}")
    print(f"CHCP: {os.environ.get('CHCP', '未设置')}")
    print()


def main():
    """主函数"""
    print("编码和\\r字符修复验证测试")
    print("=" * 60)
    
    show_environment_info()
    
    # 测试Unicode输出
    try:
        test_chars = "测试Unicode输出: 你好世界 ❤️ 🎉 🚀"
        print(test_chars)
        print("✓ Unicode字符输出正常")
    except UnicodeEncodeError as e:
        print(f"✗ Unicode字符输出错误: {e}")
        print("建议使用 start_utf8.bat 启动")
        return False
    
    print()
    
    # 运行任务管理器测试
    success = test_task_manager_fixes()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 修复验证完成！")
        print("\n修复内容:")
        print("1. ✓ 中文和Unicode字符正确显示")
        print("2. ✓ \\r字符正确处理（覆盖而不是换行）")
        print("3. ✓ Windows UTF-8编码支持")
        print("\n使用建议:")
        print("- Windows用户请使用 start_utf8.bat 启动")
        print("- 确保脚本文件保存为UTF-8编码")
    else:
        print("❌ 修复验证失败")
        print("\n可能的解决方案:")
        print("1. 确保使用 start_utf8.bat 启动")
        print("2. 检查Python版本是否为3.7+")
        print("3. 检查控制台是否支持UTF-8")
    
    return success


if __name__ == '__main__':
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
