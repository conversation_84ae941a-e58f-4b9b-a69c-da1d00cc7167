import subprocess
import sys

class CarriageReturnHandler:
    def __init__(self):
        self.current_line = []
        self.line_length = 0
    
    def process_char(self, char):
        if char == '\r':
            # 清除当前行
            print('xxxxx')
            sys.stdout.write('\r' + ' ' * self.line_length + '\r')
            self.current_line = []
            self.line_length = 0
        elif char == '\n':
            sys.stdout.write(char)
            sys.stdout.flush()
            self.current_line = []
            self.line_length = 0
        else:
            self.current_line.append(char)
            self.line_length += 1
            sys.stdout.write(char)
            sys.stdout.flush()

process = subprocess.Popen(
    r'python test_scripts\test_task.py',
    stdout=subprocess.PIPE,
    stderr=subprocess.STDOUT,
    cwd='.',
    text=True,
    encoding='gbk',
    errors='replace',
    bufsize=1,
    shell=True
)

handler = CarriageReturnHandler()
while True:
    char = process.stdout.read(1)
    if not char:
        break
    handler.process_char(char)
