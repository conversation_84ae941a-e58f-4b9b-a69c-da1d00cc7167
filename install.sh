#!/bin/bash

# 任务管理器 v2.0 安装脚本
# 支持 Linux 和 macOS

set -e

echo "=================================="
echo "任务管理器 v2.0 安装脚本"
echo "=================================="

# 检查Python版本
check_python() {
    echo "检查Python版本..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo "错误: 未找到Python。请先安装Python 3.8+"
        exit 1
    fi
    
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | awk '{print $2}')
    echo "找到Python版本: $PYTHON_VERSION"
    
    # 检查版本是否满足要求 (3.8+)
    if $PYTHON_CMD -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
        echo "✓ Python版本满足要求"
    else
        echo "错误: Python版本过低，需要3.8或更高版本"
        exit 1
    fi
}

# 检查pip
check_pip() {
    echo "检查pip..."
    
    if command -v pip3 &> /dev/null; then
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        PIP_CMD="pip"
    else
        echo "错误: 未找到pip。请先安装pip"
        exit 1
    fi
    
    echo "✓ 找到pip: $PIP_CMD"
}

# 安装依赖
install_dependencies() {
    echo "安装Python依赖..."
    
    if [ -f "requirements.txt" ]; then
        $PIP_CMD install -r requirements.txt
        echo "✓ 依赖安装完成"
    else
        echo "错误: 未找到requirements.txt文件"
        exit 1
    fi
}

# 创建配置文件
setup_config() {
    echo "设置配置文件..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            echo "✓ 已创建配置文件 .env"
            echo "请编辑 .env 文件以配置您的设置"
        else
            echo "警告: 未找到配置模板文件 .env.example"
        fi
    else
        echo "✓ 配置文件已存在"
    fi
}

# 创建必要目录
create_directories() {
    echo "创建必要目录..."
    
    mkdir -p log
    mkdir -p data
    
    echo "✓ 目录创建完成"
}

# 初始化数据库
init_database() {
    echo "初始化数据库..."
    
    $PYTHON_CMD start.py --init
    
    echo "✓ 数据库初始化完成"
}

# 运行测试
run_tests() {
    echo "运行测试..."
    
    if $PIP_CMD show pytest &> /dev/null; then
        $PYTHON_CMD start.py --test
        echo "✓ 测试完成"
    else
        echo "跳过测试 (pytest未安装)"
    fi
}

# 设置服务 (可选)
setup_service() {
    if [ "$1" = "--service" ]; then
        echo "设置系统服务..."
        
        SERVICE_FILE="/etc/systemd/system/task-manager.service"
        CURRENT_DIR=$(pwd)
        CURRENT_USER=$(whoami)
        
        if [ "$EUID" -ne 0 ]; then
            echo "需要root权限来创建系统服务"
            echo "请使用sudo运行: sudo $0 --service"
            return
        fi
        
        cat > $SERVICE_FILE << EOF
[Unit]
Description=Task Manager Service
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$CURRENT_DIR
ExecStart=$PYTHON_CMD $CURRENT_DIR/start.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
        
        systemctl daemon-reload
        systemctl enable task-manager
        
        echo "✓ 系统服务已创建"
        echo "使用以下命令管理服务:"
        echo "  启动: sudo systemctl start task-manager"
        echo "  停止: sudo systemctl stop task-manager"
        echo "  状态: sudo systemctl status task-manager"
    fi
}

# 显示完成信息
show_completion() {
    echo ""
    echo "=================================="
    echo "安装完成!"
    echo "=================================="
    echo ""
    echo "启动方式:"
    echo "  开发模式: python start.py"
    echo "  生产模式: python start.py --host 0.0.0.0"
    echo "  调试模式: python start.py --debug"
    echo ""
    echo "Docker部署:"
    echo "  构建镜像: docker build -t task-manager ."
    echo "  运行容器: docker-compose up -d"
    echo ""
    echo "访问地址: http://localhost:5001"
    echo ""
    echo "更多信息请查看 README.md"
}

# 主安装流程
main() {
    check_python
    check_pip
    install_dependencies
    setup_config
    create_directories
    init_database
    run_tests
    setup_service "$1"
    show_completion
}

# 运行安装
main "$1"
