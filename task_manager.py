import os
import time
import sqlite3
import subprocess
import threading
from datetime import datetime
from pathlib import Path
from croniter import croniter
import locale
from notification import Notification
from database import DatabasePool, DatabaseManager
from scheduler import TaskScheduler
from log_manager import LogManager
from monitoring import SystemMonitor
from config import config




class Task_manager:
    def __init__(self, db_path=None, log_dir=None, max_logs=None, smtp_config=None):
        """
        初始化任务管理器

        参数:
            db_path: SQLite数据库路径（可选，默认使用配置文件）
            log_dir: 日志存储目录（可选，默认使用配置文件）
            max_logs: 最大日志数量（可选，默认使用配置文件）
            smtp_config: SMTP配置，用于邮件通知（可选，默认使用配置文件）
        """
        # 使用传入参数或配置文件中的默认值
        self.db_path = db_path or config.DB_PATH
        self.log_dir = Path(log_dir or config.LOG_DIR)
        self.max_logs = max_logs or config.MAX_LOGS

        # 初始化数据库连接池
        self.db_pool = DatabasePool(self.db_path, max_connections=10)
        self.db_manager = DatabaseManager(self.db_pool)

        # 初始化日志管理器
        self.log_manager = LogManager(
            log_dir=str(self.log_dir),
            max_logs=self.max_logs,
            max_log_size=getattr(config, 'MAX_LOG_SIZE', 10 * 1024 * 1024),
            retention_days=getattr(config, 'LOG_RETENTION_DAYS', 30),
            compress_after_days=getattr(config, 'LOG_COMPRESS_AFTER_DAYS', 7),
            compression_enabled=getattr(config, 'LOG_COMPRESSION_ENABLED', True)
        )

        # 存储任务ID和进程信息
        self.running_tasks = {}

        # 获取系统编码
        system_encoding = locale.getpreferredencoding()
        print(f"系统编码: {system_encoding}")
        self.encoding = system_encoding

        # 初始化通知系统
        smtp_config = smtp_config or config.get_smtp_config()
        self.notification = Notification(smtp_config=smtp_config)

        # 在启动任务调度器前，将所有运行中的任务标记为失败
        self._mark_running_tasks_as_failed()

        # 初始化任务调度器
        max_concurrent = getattr(config, 'MAX_CONCURRENT_TASKS', 10)
        self.scheduler = TaskScheduler(self, max_concurrent_tasks=max_concurrent)

        # 初始化系统监控器
        if getattr(config, 'MONITORING_ENABLED', True):
            monitoring_interval = getattr(config, 'MONITORING_INTERVAL', 60)
            self.monitor = SystemMonitor(self, collection_interval=monitoring_interval)
            self.monitor.start_monitoring()
        else:
            self.monitor = None

        # 启动调度器
        self.scheduler.start()
    
    def set_smtp_config(self, smtp_config):
        """
        设置SMTP配置
        
        参数:
            smtp_config: SMTP配置，包含以下字段:
                - host: SMTP服务器地址
                - port: SMTP服务器端口
                - username: SMTP用户名
                - password: SMTP密码
                - sender: 发件人邮箱
                - use_ssl: 是否使用SSL (默认True)
        """
        self.notification.set_smtp_config(smtp_config)
    
    def stop(self):
        """
        停止任务管理器
        """
        print("正在停止任务管理器...")

        # 停止调度器
        if hasattr(self, 'scheduler'):
            self.scheduler.stop()

        # 停止监控器
        if hasattr(self, 'monitor') and self.monitor:
            self.monitor.stop_monitoring()

        # 停止日志管理器
        if hasattr(self, 'log_manager'):
            self.log_manager.stop_cleanup_thread()

        # 关闭数据库连接池
        if hasattr(self, 'db_pool'):
            self.db_pool.close_all()

        print("任务管理器已停止")
    
    def add_task(self, script_path, working_dir, cron_expression, name="", max_runtime=3600, retry_on_error=False, remark="", check_name_exists=True):
        """
        添加定时任务

        参数:
            script_path: 脚本路径
            working_dir: 工作目录
            cron_expression: crontab格式的时间表达式 (分 时 日 月 周)
            name: 任务名称
            max_runtime: 最大运行时间(秒)
            retry_on_error: 失败是否重试
            remark: 任务备注
            check_name_exists: 是否检查任务名称是否已存在

        返回:
            task_id: 任务ID
        """
        # 验证crontab表达式
        try:
            croniter(cron_expression)
        except ValueError:
            raise ValueError("无效的crontab表达式")

        # 如果任务名称已存在，则返回任务ID
        if check_name_exists and name:
            tasks = self.get_tasks()
            existing_task = next((task for task in tasks if task['name'] == name), None)
            if existing_task:
                return existing_task['id']

        # 使用数据库管理器插入任务
        task_id = self.db_manager.execute_insert(
            "INSERT INTO tasks (name, script_path, working_dir, max_runtime, retry_on_error, cron_expression, remark) VALUES (?, ?, ?, ?, ?, ?, ?)",
            (name, script_path, working_dir, max_runtime, 1 if retry_on_error else 0, cron_expression, remark)
        )

        # 获取新添加的任务信息并通知调度器
        task_info = self.get_task(task_id)
        if task_info and hasattr(self, 'scheduler'):
            self.scheduler.add_task(task_info)

        return task_id
    
    def add_notification(self, task_id, notification_type, config, notify_on_error=True):
        """
        为任务添加通知配置
        
        参数:
            task_id: 任务ID
            notification_type: 通知类型 ('email' 或 'webhook')
            config: 通知配置（JSON字符串或字典）
            notify_on_error: 是否仅在任务执行出错时发送通知，默认为True
            
        返回:
            config_id: 通知配置ID
        """
        import json
        
        # 检查任务是否存在
        task = self.get_task(task_id)
        if not task:
            raise ValueError(f"任务ID {task_id} 不存在")
        
        # 检查通知类型
        if notification_type not in ['email', 'webhook']:
            raise ValueError("通知类型必须是 'email' 或 'webhook'")
        
        # 如果配置是字典，转换为JSON字符串
        if isinstance(config, dict):
            config = json.dumps(config)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        # 检查是否已存在相同的通知配置
        cursor.execute(
            "SELECT id,is_active FROM notification_config WHERE task_id = ? AND notification_type = ? AND config = ?",
            (task_id, notification_type, config)
        )
        row=cursor.fetchone()
        if row:
            # 如果通知配置已存在，并且是激活的，则返回通知配置ID
            if row[1]==1:
                # 更新notify_on_error字段
                cursor.execute("UPDATE notification_config SET notify_on_error = ? WHERE id = ?", 
                              (1 if notify_on_error else 0, row[0]))
                conn.commit()
                conn.close()
                return row[0]
            else:
                # 如果通知配置已存在，并且是未激活的，则更新为激活
                cursor.execute("UPDATE notification_config SET is_active = 1, notify_on_error = ? WHERE id = ?", 
                              (1 if notify_on_error else 0, row[0]))
                conn.commit()
                conn.close()
                return row[0]
        cursor.execute(
            "INSERT INTO notification_config (task_id, notification_type, config, is_active, notify_on_error) VALUES (?, ?, ?, 1, ?)",
            (task_id, notification_type, config, 1 if notify_on_error else 0)
        )
        
        config_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return config_id
    
    def remove_notification(self, config_id):
        """
        移除通知配置
        
        参数:
            config_id: 通知配置ID
            
        返回:
            success: 是否成功移除
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("UPDATE notification_config SET is_active = 0 WHERE id = ?", (config_id,))
        
        success = cursor.rowcount > 0
        conn.commit()
        conn.close()
        
        return success
    
    def get_notifications(self, task_id=None):
        """
        获取通知配置
        
        参数:
            task_id: 任务ID，如不指定则获取所有通知配置
            
        返回:
            notifications: 通知配置列表
        """
        import json
        
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        if task_id is None:
            cursor.execute("SELECT * FROM notification_config WHERE is_active = 1")
        else:
            cursor.execute("SELECT * FROM notification_config WHERE task_id = ? AND is_active = 1", (task_id,))
        
        notifications = []
        for row in cursor.fetchall():
            notification = dict(row)
            # 将JSON字符串转换为字典
            try:
                notification['config'] = json.loads(notification['config'])
            except:
                pass
            notifications.append(notification)
        
        conn.close()
        
        return notifications
    
    def remove_task(self, task_id):
        """
        移除定时任务
        
        参数:
            task_id: 任务ID
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("UPDATE tasks SET is_active = 0 WHERE id = ?", (task_id,))
        
        conn.commit()
        conn.close()
        
        # 通知调度器更新任务
        self.task_update_event.set()
    
    def edit_task(self, task_id, script_path=None, working_dir=None, cron_expression=None, 
                 name=None, max_runtime=None, retry_on_error=None, remark=None, is_active=None):
        """
        编辑定时任务
        
        参数:
            task_id: 任务ID
            script_path: 脚本路径
            working_dir: 工作目录
            cron_expression: crontab格式的时间表达式 (分 时 日 月 周)
            name: 任务名称
            max_runtime: 最大运行时间(秒)
            retry_on_error: 失败是否重试
            remark: 任务备注
            is_active: 是否激活任务
            
        返回:
            success: 是否成功编辑
        """
        # 验证crontab表达式
        if cron_expression is not None:
            try:
                croniter(cron_expression)
            except ValueError:
                raise ValueError("无效的crontab表达式")
        
        # 获取当前任务信息
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM tasks WHERE id = ?", (task_id,))
        task = cursor.fetchone()
        
        if task is None:
            conn.close()
            return False
        
        # 准备更新字段
        update_fields = []
        params = []
        
        if script_path is not None:
            update_fields.append("script_path = ?")
            params.append(script_path)
        
        if working_dir is not None:
            update_fields.append("working_dir = ?")
            params.append(working_dir)
        
        if cron_expression is not None:
            update_fields.append("cron_expression = ?")
            params.append(cron_expression)
        
        if name is not None:
            update_fields.append("name = ?")
            params.append(name)
        
        if max_runtime is not None:
            update_fields.append("max_runtime = ?")
            params.append(max_runtime)
        
        if retry_on_error is not None:
            update_fields.append("retry_on_error = ?")
            params.append(1 if retry_on_error else 0)
        
        if remark is not None:
            update_fields.append("remark = ?")
            params.append(remark)
        
        if is_active is not None:
            update_fields.append("is_active = ?")
            params.append(1 if is_active else 0)
        
        # 如果没有要更新的字段，直接返回
        if not update_fields:
            conn.close()
            return True
        
        # 执行更新
        query = f"UPDATE tasks SET {', '.join(update_fields)} WHERE id = ?"
        params.append(task_id)
        
        cursor.execute(query, params)
        conn.commit()
        conn.close()
        
        # 通知调度器更新任务
        self.task_update_event.set()
        
        return True
    
    def run_task_now(self, task_id):
        """
        立即手动运行任务，不影响定时调度
        
        参数:
            task_id: 任务ID
            
        返回:
            success: 是否成功启动任务
            message: 提示消息
        """
        print(f"立即手动运行任务: {task_id}")
        # 检查任务是否存在
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM tasks WHERE id = ?", (task_id,))
        task = cursor.fetchone()
        conn.close()
        
        if task is None:
            return False, "任务不存在"
        
        # 检查任务是否已经在运行
        if task_id in self.running_tasks:
            return False, "任务已在运行中"
        
        # 获取任务是否禁用的状态
        is_disabled = task['is_active'] == 0
        
        # 在新线程中执行任务，即使是禁用状态也允许手动执行
        task_dict = dict(task)
        thread = threading.Thread(
            target=self._execute_task,
            args=(task_dict,)
        )
        thread.daemon = True
        thread.start()
        
        return True, "禁用的任务开始执行" if is_disabled else "任务已启动"
    
    def get_task(self, task_id=None , name=None):
        """
        获取任务
        """
        if not task_id and not name:
            return None
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        if task_id:
            cursor.execute("SELECT * FROM tasks WHERE id = ?", (task_id,))
        elif name:
            cursor.execute("SELECT * FROM tasks WHERE name = ? and is_active = 1", (name,))
        task = cursor.fetchone()
        conn.close()
        return dict(task) if task else None
    
    def get_tasks(self, include_inactive=False):
        """
        获取所有任务

        参数:
            include_inactive: 是否包含已停用的任务

        返回:
            tasks: 任务列表
        """
        if include_inactive:
            query = "SELECT * FROM tasks ORDER BY id"
            params = ()
        else:
            query = "SELECT * FROM tasks WHERE is_active = 1 ORDER BY id"
            params = ()

        return self.db_manager.execute_query(query, params)

    def get_task(self, task_id):
        """
        获取单个任务信息

        参数:
            task_id: 任务ID

        返回:
            task: 任务信息字典，如果不存在则返回None
        """
        tasks = self.db_manager.execute_query("SELECT * FROM tasks WHERE id = ?", (task_id,))
        return tasks[0] if tasks else None
    
    def get_task_logs(self, task_id=None, limit=10):
        """
        获取任务日志记录
        
        参数:
            task_id: 任务ID，如不指定则获取所有任务的日志
            limit: 返回的最大记录数
            
        返回:
            logs: 日志记录列表
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        if task_id is None:
            cursor.execute(
                "SELECT * FROM task_runs ORDER BY start_time DESC LIMIT ?",
                (limit,)
            )
        else:
            cursor.execute(
                "SELECT * FROM task_runs WHERE task_id = ? ORDER BY start_time DESC LIMIT ?",
                (task_id, limit)
            )
        
        logs = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return logs
    
    def get_log_content(self, log_id):
        """
        获取日志文件内容
        
        参数:
            log_id: 日志记录ID
            
        返回:
            content: 日志内容字符串
        """
        # 根据日志ID获取日志文件名
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT log_file FROM task_runs WHERE id = ?", (log_id,))
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            return "日志记录不存在"
            
        log_file = result[0]
        log_path = self.log_dir / log_file
        
        if not log_path.exists():
            return "日志文件不存在"
        
        try:
            with open(log_path, 'r', encoding='utf-8', errors='replace') as f:
                return f.read()
        except Exception as e:
            return f"读取日志文件时发生错误: {str(e)}"
    def _scheduler_loop(self):
        """任务调度器循环，检查并执行到期的任务"""
        task_queue = []
        # print(f"任务调度器循环开始")
        # 主循环
        while not self.stop_event.is_set():
            # 检查是否需要更新任务队列
            if self.task_update_event.is_set() or not task_queue:
                # 重新加载所有活动任务
                task_queue = self._reload_tasks()
                self.task_update_event.clear()
                
                # 如果没有任务，等待一段时间后继续
                if not task_queue:
                    time.sleep(1)
                    continue
            
            # 查看最早执行的任务
            next_time, task_id, task = task_queue[0]
            current_time = datetime.now()

            if current_time >= next_time:
                # 任务到期，检查任务是否仍然活跃并获取最新的任务信息
                if self._is_task_active(task_id) and task_id not in self.running_tasks:
                    # 获取最新的任务信息，以防任务被编辑
                    updated_task = self._get_task_by_id(task_id)
                    # print(f"获取最新的任务信息: {updated_task}")
                    if updated_task:
                        thread = threading.Thread(
                            target=self._execute_task,
                            args=(updated_task,)
                        )
                        thread.daemon = True
                        thread.start()

                # 计算下一次执行时间并更新队列
                # 获取最新的cron表达式，以防它被编辑
                updated_cron = self._get_task_cron(task_id) or task['cron_expression']
                cron = croniter(updated_cron, next_time)
                next_next_time = cron.get_next(datetime)
                heapq.heappop(task_queue)  # 移除已处理的任务
                
                # 只有当任务仍然活跃时才添加回队列
                if self._is_task_active(task_id):
                    # 获取最新的任务信息
                    updated_task = self._get_task_by_id(task_id) or task
                    heapq.heappush(task_queue, (next_next_time, task_id, updated_task))
            else:
                # 未到期，等待到下一次执行时间（最多等待1秒以检查停止事件和任务更新）
                wait_seconds = (next_time - current_time).total_seconds()
                wait_time = min(wait_seconds, 1)
                
                # 使用超时等待，以便能够响应任务更新事件
                self.task_update_event.wait(timeout=wait_time)
    
    def _get_task_by_id(self, task_id):
        """获取任务的最新信息"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM tasks WHERE id = ?", (task_id,))
        task = cursor.fetchone()
        conn.close()
        
        return dict(task) if task else None
    
    def _get_task_cron(self, task_id):
        """获取任务的最新cron表达式"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT cron_expression FROM tasks WHERE id = ?", (task_id,))
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result else None
    
    def _reload_tasks(self):
        """重新加载所有活动任务并返回优先队列"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM tasks WHERE is_active = 1")
        tasks = [dict(row) for row in cursor.fetchall()]
        conn.close()

        # 初始化任务队列
        task_queue = []
        for task in tasks:
            cron = croniter(task['cron_expression'], datetime.now())
            next_time = cron.get_next(datetime)
            heapq.heappush(task_queue, (next_time, task['id'], task))
            
        return task_queue
    
    def _is_task_active(self, task_id):
        """检查任务是否仍然活跃"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT is_active FROM tasks WHERE id = ?", (task_id,))
        result = cursor.fetchone()
        conn.close()
        
        return result is not None and result[0] == 1
    
    def _execute_task(self, task):
        """
        执行任务

        参数:
            task: 任务信息字典
        """
        task_id = task['id']
        script_path = task['script_path']
        working_dir = task['working_dir']
        max_runtime = task['max_runtime']

        # 使用日志管理器创建日志文件
        start_time = datetime.now()
        log_path = self.log_manager.create_log_file(task_id)
        log_filename = log_path.name

        # 记录任务开始
        run_id = self.db_manager.execute_insert(
            "INSERT INTO task_runs (task_id, start_time, log_file, status) VALUES (?, ?, ?, ?)",
            (task_id, start_time.strftime('%Y-%m-%d %H:%M:%S'), log_filename, 'running')
        )

        # 打开日志文件并保存对象
        log_file = open(log_path, 'w', encoding='utf-8', errors='replace', buffering=1)
        self.running_tasks[task_id] = {
            "run_id": run_id,
            "log_path": log_path,
            "process": None,
            "log_file_obj": log_file,
            "start_time": start_time
        }
        
        try:
            now_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_file.write(f"\n{now_time} 任务开始执行\n")
            try:
                # 设置环境变量强制使用UTF-8编码
                env = os.environ.copy()
                env['PYTHONIOENCODING'] = 'utf-8'
                env['PYTHONUTF8'] = '1'  # Python 3.7+

                process = subprocess.Popen(
                    script_path,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    cwd=working_dir,
                    text=True,
                    encoding='utf-8',  # 强制使用UTF-8
                    errors='replace',  # 遇到无法解码的字符时替换
                    bufsize=1,
                    shell=True,  # 让脚本支持&&这样的拼接
                    env=env  # 传递环境变量
                )
                if task_id in self.running_tasks:
                    self.running_tasks[task_id]["process"] = process
                end_time = start_time
                status = 'success'
                process_running = True
                def read_output():
                    nonlocal process_running
                    while process_running:
                        line = process.stdout.readline()
                        if not line and process.poll() is not None:
                            break
                        if line:
                            # 处理\r字符：将\r替换为\n，避免日志显示问题
                            processed_line = line.replace('\r', '\n')
                            # 确保行末有换行符
                            if not processed_line.endswith('\n'):
                                processed_line += '\n'
                            log_file.write(processed_line)
                            log_file.flush()
                            os.fsync(log_file.fileno())
                output_thread = threading.Thread(target=read_output)
                output_thread.daemon = True
                output_thread.start()
                while process.poll() is None:
                    time.sleep(1)
                    current_time = datetime.now()
                    if (current_time - start_time).total_seconds() > max_runtime:
                        process.terminate()
                        now_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        log_file.write(f"\n{now_time} 任务因超时{max_runtime}秒终止\n")
                        log_file.flush()
                        os.fsync(log_file.fileno())
                        status = 'timeout'
                        break
                process_running = False
                output_thread.join(timeout=5)
                process.wait(timeout=10)
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                if process.returncode != 0 and status == 'success':
                    status = 'error'
            finally:
                # 不在这里关闭log_file，交由finally统一关闭
                pass
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            status = 'error'
            log_file.write(f"\n执行过程中发生异常: {str(e)}\n")
            log_file.flush()
            os.fsync(log_file.fileno())
        finally:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT status FROM task_runs WHERE id = ?", (run_id,))
            current_status = cursor.fetchone()[0]
            if current_status != 'terminated':
                cursor.execute(
                    "UPDATE task_runs SET end_time = ?, duration = ?, status = ? WHERE id = ?",
                    (end_time.strftime('%Y-%m-%d %H:%M:%S'), duration, status, run_id)
                )
                conn.commit()
            conn.close()
            # 关闭日志文件对象
            if task_id in self.running_tasks and self.running_tasks[task_id].get("log_file_obj"):
                try:
                    self.running_tasks[task_id]["log_file_obj"].close()
                except Exception:
                    pass
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            self._send_task_notifications(task_id, {
                'id': task_id,
                'name': task.get('name', f'Task {task_id}'),
                'status': status,
                'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
                'duration': duration,
                'log_file': log_filename
            })
            self._cleanup_logs()

    def terminate_task(self, task_id):
        """
        终止正在运行的任务
        """
        if task_id not in self.running_tasks:
            return False, "任务未在运行中"
        try:
            task_info = self.running_tasks[task_id]
            process = task_info.get("process")
            run_id = task_info.get("run_id")
            log_file = task_info.get("log_file_obj")
            if not process or not run_id or not log_file:
                return False, "任务信息不完整，无法终止"
            # 日志写入（同一对象）
            now_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_file.write(f"\n{now_time} 任务被手动终止\n")
            log_file.flush()
            os.fsync(log_file.fileno())
            # 终止进程
            if process.poll() is None:
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait(timeout=5)
            # 计算任务运行时长
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT start_time FROM task_runs WHERE id = ?", (run_id,))
            result = cursor.fetchone()
            if not result:
                conn.close()
                return False, "找不到任务执行记录"
            start_time_str = result[0]
            start_time = datetime.strptime(start_time_str, '%Y-%m-%d %H:%M:%S')
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            cursor.execute(
                "UPDATE task_runs SET end_time = ?, duration = ?, status = ? WHERE id = ?",
                (end_time.strftime('%Y-%m-%d %H:%M:%S'), duration, 'terminated', run_id)
            )
            conn.commit()
            conn.close()
            # 关闭日志文件对象
            try:
                log_file.close()
            except Exception:
                pass
            # 移除运行中任务
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            return True, "任务已终止"
        except Exception as e:
            return False, f"终止任务时发生错误: {str(e)}"
    
    def _send_task_notifications(self, task_id, task_run_info):
        """
        发送任务通知
        
        参数:
            task_id: 任务ID
            task_run_info: 任务运行信息
        """
        import json
        
        # 获取任务的通知配置
        notifications = self.get_notifications(task_id)
        
        for notification in notifications:
            notification_type = notification['notification_type']
            config = notification['config']
            notify_on_error = bool(notification.get('notify_on_error', True))
            
            # 检查是否需要发送通知
            # 如果notify_on_error为True，则只在任务状态为error或timeout时发送通知
            # 如果notify_on_error为False，则无论任务状态如何都发送通知
            if notify_on_error and task_run_info['status'] not in ['error', 'timeout']:
                continue
            
            try:
                # 发送通知
                self.notification.send_task_notification(
                    task_info=task_run_info,
                    notification_type=notification_type,
                    notification_config=config
                )
            except Exception as e:
                print(f"发送任务通知失败: {str(e)}")
    
    
    
    def _cleanup_logs(self):
        """清理旧日志，确保每个任务的日志数量不超过最大值"""
        # 获取所有日志文件
        log_files = list(self.log_dir.glob('*.log'))
        
        # 按任务ID分组日志文件
        task_logs = {}
        for log_file in log_files:
            # 日志文件名格式为: {task_id}_{timestamp}.log
            try:
                file_name = log_file.name
                task_id = int(file_name.split('_')[0])
                
                if task_id not in task_logs:
                    task_logs[task_id] = []
                
                # 添加日志文件及其修改时间
                mtime = log_file.stat().st_mtime
                task_logs[task_id].append((log_file, mtime))
            except (ValueError, IndexError):
                # 跳过无法解析的日志文件名
                continue
        
        # 对每个任务，保留最新的max_logs个日志文件
        for task_id, files in task_logs.items():
            # 按修改时间排序，最旧的在前面
            files.sort(key=lambda x: x[1])
            
            # 如果日志文件数量超过最大值，删除最旧的日志
            if len(files) > self.max_logs:
                excess_count = len(files) - self.max_logs
                for i in range(excess_count):
                    try:
                        os.remove(files[i][0])
                    except Exception:
                        pass  # 忽略删除失败的情况
    
    def stop(self):
        """停止任务管理器"""
        self.stop_event.set()
        self.scheduler_thread.join(timeout=5)
        
        # 强制终止所有运行中的任务
        for task_id in list(self.running_tasks.keys()):
            try:
                # 尝试优雅地终止任务
                self.terminate_task(task_id)
            except Exception:
                # 如果终止失败，直接从列表中移除
                self.running_tasks.pop(task_id, None)
    
    def _mark_running_tasks_as_failed(self):
        """将所有运行中的任务标记为失败"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 查找状态为"running"的任务运行记录
        cursor.execute("SELECT id, start_time FROM task_runs WHERE status = 'running'")
        running_tasks = cursor.fetchall()
        
        for run_id, start_time in running_tasks:
            # 计算任务结束时间和持续时间
            start_time_obj = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            end_time = datetime.now()
            duration = (end_time - start_time_obj).total_seconds()
            
            # 更新任务状态为"error"
            cursor.execute(
                "UPDATE task_runs SET end_time = ?, duration = ?, status = ? WHERE id = ?",
                (end_time.strftime('%Y-%m-%d %H:%M:%S'), duration, 'error', run_id)
            )
            
            # 向日志文件添加错误信息
            cursor.execute("SELECT log_file FROM task_runs WHERE id = ?", (run_id,))
            log_file = cursor.fetchone()[0]
            if log_file:
                log_path = self.log_dir / log_file
                if log_path.exists():
                    with open(log_path, 'a', encoding='utf-8') as f:
                        f.write(f"\n{end_time.strftime('%Y-%m-%d %H:%M:%S')} 任务因系统重启而失败\n")
        
        conn.commit()
        conn.close()
        print(f"已将 {len(running_tasks)} 个运行中的任务标记为失败")