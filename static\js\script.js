document.addEventListener('DOMContentLoaded', function() {
    // 主题切换
    const themeToggle = document.getElementById('theme-toggle');
    
    // 检查用户首选项
    if (localStorage.getItem('theme') === 'dark' || 
        (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }
    
    themeToggle.addEventListener('click', function() {
        if (document.documentElement.classList.contains('dark')) {
            document.documentElement.classList.remove('dark');
            localStorage.setItem('theme', 'light');
        } else {
            document.documentElement.classList.add('dark');
            localStorage.setItem('theme', 'dark');
        }
    });
    
    // 加载仪表盘数据
    loadDashboardData();
    
    // 加载任务列表
    loadTasks();
    
    // 设置定时刷新 (每分钟刷新)
    window.refreshInterval = setInterval(function() {
        loadDashboardData();
        loadTasks();
    }, 60000);
    
    // 任务过滤事件
    document.getElementById('task-filter').addEventListener('input', function() {
        filterAndRenderTasks();
    });
    
    document.getElementById('show-disabled').addEventListener('change', function() {
        filterAndRenderTasks();
    });
    
    // 刷新按钮
    document.getElementById('refresh-btn').addEventListener('click', function() {
        // 添加旋转动画
        this.querySelector('i').classList.add('animate-spin');
        
        // 刷新数据
        Promise.all([
            loadDashboardData(),
            loadTasks()
        ])
        .then(() => {
            // 移除旋转动画
            setTimeout(() => {
                this.querySelector('i').classList.remove('animate-spin');
                // 显示成功消息
                showNotification('数据已刷新', 'success');
            }, 500);
        })
        .catch(error => {
            // 移除旋转动画
            this.querySelector('i').classList.remove('animate-spin');
            // 显示错误消息
            showNotification('刷新数据失败', 'error');
            console.error('刷新数据失败:', error);
        });
    });
    
    // 点击数据面板显示详情
    document.getElementById('running-panel').addEventListener('click', () => showPanelDetails('running'));
    document.getElementById('completed-panel').addEventListener('click', () => showPanelDetails('completed'));
    document.getElementById('failed-panel').addEventListener('click', () => showPanelDetails('failed'));
    
    // 关闭面板详情
    document.getElementById('close-panel').addEventListener('click', () => {
        document.getElementById('panel-details').classList.add('hidden');
    });
    
    // 关闭各种弹窗
    document.getElementById('close-log-modal').addEventListener('click', closeLogModal);
    document.getElementById('close-log-list-modal').addEventListener('click', closeLogListModal);
    document.getElementById('close-task-modal').addEventListener('click', closeTaskModal);
    document.getElementById('cancel-task').addEventListener('click', closeTaskModal);
    
    // 打开添加任务弹窗
    document.getElementById('add-task-btn').addEventListener('click', () => {
        document.getElementById('task-modal-title').textContent = '添加任务';
        document.getElementById('task-form').reset();
        document.getElementById('task-id').value = '';
        document.getElementById('task-modal').classList.remove('hidden');
    });
    
    // 提交任务表单
    document.getElementById('task-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const taskId = document.getElementById('task-id').value;
        const formData = {
            name: document.getElementById('task-name').value,
            script_path: document.getElementById('script-path').value,
            working_dir: document.getElementById('working-dir').value,
            cron_expression: document.getElementById('cron-expression').value,
            max_runtime: parseInt(document.getElementById('max-runtime').value),
            retry_on_error: document.getElementById('retry-on-error').checked,
            remark: document.getElementById('task-remark').value
        };
        
        const url = taskId ? `/api/tasks/${taskId}` : '/api/tasks';
        const method = taskId ? 'PUT' : 'POST';
        
        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closeTaskModal();
                // 显示成功消息
                showNotification(taskId ? '任务更新成功' : '任务添加成功', 'success');
                loadTasks(); // 重新加载任务列表
            } else {
                // 显示错误消息
                showNotification('保存失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('保存任务失败:', error);
            // 显示错误消息
            showNotification('保存失败，请检查网络连接', 'error');
        });
    });
    
    // 点击外部关闭任务操作菜单
    document.addEventListener('click', function(e) {
        const taskMenu = document.getElementById('task-menu');
        if (taskMenu && !taskMenu.classList.contains('hidden') && !e.target.closest('#task-menu') && !e.target.classList.contains('more-btn')) {
            taskMenu.classList.add('hidden');
        }
    });
    
    // 添加ESC键关闭弹窗功能
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const logModal = document.getElementById('log-modal');
            const logListModal = document.getElementById('log-list-modal');
            const taskModal = document.getElementById('task-modal');
            const taskMenu = document.getElementById('task-menu');

            if (!logModal.classList.contains('hidden')) {
                closeLogModal();
                e.stopPropagation();
                return;
            }
            
            if (!logListModal.classList.contains('hidden')) {
                closeLogListModal();
                e.stopPropagation();
                return;
            }
            
            if (!taskModal.classList.contains('hidden')) {
                closeTaskModal();
                e.stopPropagation();
                return;
            }
            
            if (!taskMenu.classList.contains('hidden')) {
                taskMenu.classList.add('hidden');
                e.stopPropagation();
                return;
            }
        }
    });
});

// 通知系统
function showNotification(message, type = 'info', duration = 3000) {
    const container = document.getElementById('notifications-container');
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    // 根据类型选择图标
    let icon = '';
    switch(type) {
        case 'success':
            icon = '<i class="fas fa-check-circle"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-times-circle"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle"></i>';
    }
    
    // 设置内容
    notification.innerHTML = `
        <div class="notification-content">
            ${icon}
            <span>${message}</span>
        </div>
        <div class="notification-close">
            <i class="fas fa-times"></i>
        </div>
    `;
    
    // 添加到容器
    container.appendChild(notification);
    
    // 关闭按钮事件
    notification.querySelector('.notification-close').addEventListener('click', () => {
        closeNotification(notification);
    });
    
    // 自动关闭
    setTimeout(() => {
        closeNotification(notification);
    }, duration);
    
    return notification;
}

function closeNotification(notification) {
    notification.style.animation = 'slideOut 0.3s forwards';
    
    notification.addEventListener('animationend', () => {
        notification.remove();
    });
}

// 关闭日志弹窗
function closeLogModal() {
    document.getElementById('log-modal').classList.add('hidden');
    // 清除日志刷新定时器
    if (window.logRefreshInterval) {
        clearInterval(window.logRefreshInterval);
        window.logRefreshInterval = null;
    }
}

// 关闭日志列表弹窗
function closeLogListModal() {
    document.getElementById('log-list-modal').classList.add('hidden');
}

// 关闭任务弹窗
function closeTaskModal() {
    document.getElementById('task-modal').classList.add('hidden');
}

// 加载仪表盘数据
function loadDashboardData() {
    return fetch('/api/dashboard')
        .then(response => response.json())
        .then(data => {
            document.getElementById('running-count').textContent = data.running;
            document.getElementById('completed-count').textContent = data.completed;
            document.getElementById('failed-count').textContent = data.failed;
        })
        .catch(error => console.error('加载仪表盘数据失败:', error));
}

// 加载任务列表
function loadTasks() {
    return fetch('/api/tasks')
        .then(response => response.json())
        .then(tasks => {
            // 保存所有任务数据以供过滤使用
            window.allTasks = tasks;
            filterAndRenderTasks();
        })
        .catch(error => console.error('加载任务列表失败:', error));
}

// 过滤并渲染任务列表
function filterAndRenderTasks() {
    if (!window.allTasks) return;
    
    const filterText = document.getElementById('task-filter').value.toLowerCase();
    const showDisabled = document.getElementById('show-disabled').checked;
    
    let filteredTasks = window.allTasks.filter(task => {
        // 首先检查是否显示禁用任务
        if (!showDisabled && task.is_active === 0) {
            return false;
        }
        
        // 然后检查过滤文本
        const taskName = (task.name || `任务 ${task.id}`).toLowerCase();
        const scriptPath = (task.script_path || '').toLowerCase();
        const remark = (task.remark || '').toLowerCase();
        
        return taskName.includes(filterText) || 
               scriptPath.includes(filterText) || 
               remark.includes(filterText);
    });
    
    renderTaskList(filteredTasks);
}

// 渲染任务列表
function renderTaskList(tasks) {
    const taskList = document.getElementById('tasks-container');
    taskList.innerHTML = '';
    
    if (tasks.length === 0) {
        taskList.innerHTML = '<tr><td colspan="6" class="text-center py-4">没有找到任务</td></tr>';
        return;
    }
    
    // 存储所有任务，以便其他函数可以访问
    window.allTasks = tasks;
    
    tasks.forEach(task => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50 dark:hover:bg-gray-800';
        
        // 为禁用的任务添加不同的样式
        if (task.is_active === 0) {
            row.className += ' opacity-80'; // 提高整行的不透明度，让内容更清晰
        }
        
        row.setAttribute('data-task-id', task.id);
        
        // 格式化耗时
        const duration = task.duration ? formatDuration(task.duration) : '-';
        
        // 状态标签
        const statusLabel = createStatusLabel(task.status);
        
        // 为禁用任务的执行按钮设置样式
        let runBtnClass = 'run-btn action-btn hover:text-blue-600 dark:hover:text-blue-400';
        let runBtnIcon = '<i class="fas fa-play"></i>';
        let runBtnTitle = task.is_active === 0 ? '手动执行 (任务已禁用)' : '执行';
        
        // 如果任务正在运行，显示终止按钮
        if (task.status === 'running') {
            runBtnClass = 'terminate-btn action-btn hover:text-red-600 dark:hover:text-red-400';
            runBtnIcon = '<i class="fas fa-stop"></i>';
            runBtnTitle = '终止';
        }
        
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium ${task.is_active === 0 ? 'line-through text-gray-500 dark:text-gray-400' : ''}">${task.name || `任务 ${task.id}`}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm">${task.cron_expression || '-'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm">${task.start_time || '-'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm">${duration}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                ${statusLabel}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 actions-cell">
                <div class="flex space-x-4 w-full justify-center">
                    <button class="${runBtnClass}" data-task-id="${task.id}" title="${runBtnTitle}">
                        ${runBtnIcon}
                    </button>
                    <button class="log-btn action-btn hover:text-blue-600 dark:hover:text-blue-400" data-task-id="${task.id}" title="查看日志">
                        <i class="fas fa-file-alt"></i>
                    </button>
                    <button class="more-btn action-btn hover:text-blue-600 dark:hover:text-blue-400" data-task-id="${task.id}" data-is-active="${task.is_active}" title="更多操作">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </td>
        `;
        
        taskList.appendChild(row);
    });
    
    // 添加任务按钮事件
    document.querySelectorAll('.run-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const taskId = this.getAttribute('data-task-id');
            runTask(taskId);
        });
    });
    
    document.querySelectorAll('.terminate-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const taskId = this.getAttribute('data-task-id');
            terminateTask(taskId);
        });
    });
    
    document.querySelectorAll('.log-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const taskId = this.getAttribute('data-task-id');
            showTaskLogs(taskId);
        });
    });
    
    document.querySelectorAll('.more-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const taskId = this.getAttribute('data-task-id');
            const isActive = this.getAttribute('data-is-active') === '1';
            showTaskMenu(taskId, this, isActive);
        });
    });
}

// 显示面板详情
function showPanelDetails(type) {
    const panelDetails = document.getElementById('panel-details');
    const panelTitle = document.getElementById('panel-title');
    
    // 如果面板已经显示，且标题相同，则关闭面板
    if (!panelDetails.classList.contains('hidden') && panelTitle.textContent === getPanelTitle(type)) {
        panelDetails.classList.add('hidden');
        return;
    }
    
    let endpoint = '';
    
    switch(type) {
        case 'running':
            endpoint = '/api/dashboard/running';
            break;
        case 'completed':
            endpoint = '/api/dashboard/completed';
            break;
        case 'failed':
            endpoint = '/api/dashboard/failed';
            break;
    }
    
    panelTitle.textContent = getPanelTitle(type);
    panelDetails.classList.remove('hidden');
    
    fetch(endpoint)
        .then(response => response.json())
        .then(tasks => {
            const panelTasks = document.getElementById('panel-tasks');
            panelTasks.innerHTML = '';
            
            tasks.forEach(task => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 dark:hover:bg-gray-800';
                
                // 格式化耗时
                const duration = task.duration ? formatDuration(task.duration) : '-';
                
                // 显示任务名称，优先使用task_name属性
                const taskName = task.task_name || task.name || `任务 ${task.id}`;
                if (type === 'running') {
                    task_id = task.task_id;
                }else{
                    task_id = -1;
                }
                row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium">${taskName}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm">${task.start_time || '-'}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm">${duration}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 actions-cell">
                        <button class="panel-log-btn action-btn hover:text-blue-600 dark:hover:text-blue-400" data-task-id="${task.id}" task-id="${task_id}" data-latest-run="true" title="查看日志">
                            <i class="fas fa-file-alt"></i> 查看日志
                        </button>
                    </td>
                `;
                
                panelTasks.appendChild(row);
            });
            let autoRefresh = false;
            if (type === 'running') {
                autoRefresh = true;
            }
            // 添加日志按钮事件
            document.querySelectorAll('.panel-log-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const taskId = this.getAttribute('data-task-id');
                    const task_id = this.getAttribute('task-id');
                    const isLatestRun = this.getAttribute('data-latest-run') === 'true';
                    if (isLatestRun) {
                        // 直接获取任务最新的一条日志
                        //http://127.0.0.1:5001/api/logs/35
                        fetch(`/api/logs/${taskId}`)
                            .then(response => response.json())
                            .then(logs => {
                                if (logs.content) {
                                    // 如果返回的是单个日志对象（包含content字段）
                                    logs.task_id = task_id;
                                    showLogContent(taskId, logs, autoRefresh);
                                } else if (Array.isArray(logs) && logs.length > 0) {
                                    // 如果返回的是日志数组
                                    const latestLog = logs[0];
                                    showLogContent(latestLog.id, latestLog, autoRefresh);
                                } else {
                                    showNotification('该任务没有日志记录', 'info');
                                }
                            })
                            .catch(error => console.error('获取任务日志失败:', error));
                    } else {
                        showTaskLogs(taskId);
                    }
                });
            });
        })
        .catch(error => console.error('加载面板详情失败:', error));
}

// 获取面板标题的辅助函数
function getPanelTitle(type) {
    switch(type) {
        case 'running':
            return '执行中的任务';
        case 'completed':
            return '已完成的任务';
        case 'failed':
            return '失败的任务';
        default:
            return '';
    }
}

// 显示任务日志列表
function showTaskLogs(taskId) {
    // 使用Promise.all并行请求任务信息和日志列表
    Promise.all([
        fetch('/api/tasks').then(response => response.json()),
        fetch(`/api/tasks/${taskId}/logs`).then(response => response.json())
    ])
    .then(([tasks, logs]) => {
        // 从任务列表中获取任务名称
        const task = tasks.find(t => t.id == taskId);
        const taskName = task ? task.name : `任务 ${taskId}`;
        document.getElementById('log-list-modal-title').textContent = `${taskName} - 日志列表`;
        
        // 存储任务名称用于日志内容显示
        window.currentTaskName = taskName;
        
        const logsTable = document.getElementById('task-logs');
        logsTable.innerHTML = '';
        
        if (logs.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    无日志记录
                </td>
            `;
            logsTable.appendChild(row);
        } else {
            logs.forEach(log => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 dark:hover:bg-gray-800';
                
                // 格式化耗时
                const duration = log.duration ? formatDuration(log.duration) : '-';
                
                // 状态标签
                const statusLabel = createStatusLabel(log.status);
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm">${log.start_time || '-'}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm">${log.end_time || '-'}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm">${duration}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        ${statusLabel}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 actions-cell">
                        <button class="view-log-btn action-btn hover:text-blue-600 dark:hover:text-blue-400" data-log-id="${log.id}" title="查看详情">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                    </td>
                `;
                
                logsTable.appendChild(row);
            });
            
            // 添加查看日志按钮事件
            document.querySelectorAll('.view-log-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const logId = this.getAttribute('data-log-id');
                    showLogContent(logId, logs.find(log => log.id == logId));
                });
            });
        }
        
        document.getElementById('log-list-modal').classList.remove('hidden');
    })
    .catch(error => console.error('加载任务日志失败:', error));
}

// 显示日志内容
function showLogContent(logId, logInfo, autoRefresh = false) {
    fetch(`/api/logs/${logId}`)
        .then(response => response.json())
        .then(data => {
            // 显示任务名称和日志时间
            const taskName = logInfo && logInfo.task_name ? logInfo.task_name : window.currentTaskName || '';
            const startTime = logInfo && logInfo.start_time ? logInfo.start_time : '';
            let title = '日志详情';
            if (taskName) {
                title = `${taskName} - 日志详情 - ${startTime}`;
            } else if (startTime) {
                title = `日志详情 - ${startTime}`;
            }
            
            document.getElementById('log-modal-title').textContent = title;
            const logContentElement = document.getElementById('log-content');
            logContentElement.textContent = data.content;
            document.getElementById('log-modal').classList.remove('hidden');
            
            // 滚动到日志底部
            const logContainer = logContentElement.parentElement;
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 如果日志是正在执行中的，则每1秒自动刷新一次
            if ((autoRefresh || (logInfo && logInfo.status === 'running'))) {
                // 清除之前的定时器
                if (window.logRefreshInterval) {
                    clearInterval(window.logRefreshInterval);
                }
                
                let isRunning = true;
                
                window.logRefreshInterval = setInterval(() => {
                    // 获取最新的日志内容
                    fetch(`/api/logs/${logId}`)
                        .then(response => response.json())
                        .then(refreshData => {
                            const logContentElement = document.getElementById('log-content');
                            logContentElement.textContent = refreshData.content;
                            
                            // 判断是否需要滚动到底部
                            const logContainer = logContentElement.parentElement;
                            const isScrolledToBottom = logContainer.scrollHeight - logContainer.clientHeight <= logContainer.scrollTop + 50;
                            
                            // 如果之前已经滚动到底部或接近底部，则更新后自动滚动到底部
                            if (isScrolledToBottom) {
                                logContainer.scrollTop = logContainer.scrollHeight;
                            }
                            
                            // 获取最新的任务状态
                            return fetch(`/api/tasks/${logInfo.task_id}/logs`);
                        })
                        .then(response => response.json())
                        .then(logs => {
                            const updatedLog = logs.find(log => log.id == logId);
                            
                            // 如果任务已经完成（不再是running状态）
                            if (updatedLog && updatedLog.status !== 'running' && isRunning) {
                                isRunning = false;
                                
                                // 更新标题显示最终状态
                                const statusText = getStatusText(updatedLog.status);
                                document.getElementById('log-modal-title').textContent = 
                                    `${taskName} - 日志详情 - ${startTime} [${statusText}]`;
                                
                                // 显示完成通知
                                showNotification(`任务已${statusText}`, updatedLog.status === 'success' ? 'success' : 'error');
                                
                                // 刷新所有面板数据
                                loadDashboardData();
                                loadTasks();
                                
                                // 如果面板详情已打开，也需要更新
                                const panelDetails = document.getElementById('panel-details');
                                if (!panelDetails.classList.contains('hidden')) {
                                    const panelTitle = document.getElementById('panel-title');
                                    if (panelTitle.textContent === '执行中的任务') {
                                        showPanelDetails('running');
                                    } else if (panelTitle.textContent === '已完成的任务') {
                                        showPanelDetails('completed');
                                    } else if (panelTitle.textContent === '失败的任务') {
                                        showPanelDetails('failed');
                                    }
                                }
                                
                                // 停止轮询
                                clearInterval(window.logRefreshInterval);
                            }
                        })
                        .catch(error => console.error('刷新日志内容失败:', error));
                }, 1000);
                
                // 当关闭弹窗时清除定时器
                document.getElementById('close-log-modal').addEventListener('click', closeLogModal);
            }
        })
        .catch(error => console.error('加载日志内容失败:', error));
}

// 获取状态文本
function getStatusText(status) {
    switch (status) {
        case 'success':
            return '成功';
        case 'error':
            return '失败';
        case 'running':
            return '运行中';
        case 'timeout':
            return '超时';
        case 'pending':
            return '等待中';
        case 'terminated':
            return '已终止';
        default:
            return status || '未知';
    }
}

// 显示任务操作菜单
function showTaskMenu(taskId, button, isActive) {
    const menu = document.getElementById('task-menu');
    menu.classList.remove('hidden');
    
    const rect = button.getBoundingClientRect();
    menu.style.top = `${rect.bottom + window.scrollY}px`;
    menu.style.left = `${rect.left + window.scrollX - 100}px`;
    
    // 设置菜单项点击事件
    document.getElementById('edit-task').onclick = () => {
        menu.classList.add('hidden');
        editTask(taskId);
    };
    
    // 动态更新禁用/启用按钮显示
    const disableBtn = document.getElementById('disable-task');
    if (isActive) {
        disableBtn.innerHTML = '<i class="fas fa-ban mr-2"></i> 禁用';
        disableBtn.onclick = () => {
            menu.classList.add('hidden');
            disableTask(taskId);
        };
    } else {
        disableBtn.innerHTML = '<i class="fas fa-play-circle mr-2"></i> 启用';
        disableBtn.onclick = () => {
            menu.classList.add('hidden');
            enableTask(taskId);
        };
    }
    
    document.getElementById('delete-task').onclick = () => {
        menu.classList.add('hidden');
        deleteTask(taskId);
    };
    
    document.getElementById('pin-task').onclick = () => {
        menu.classList.add('hidden');
        pinTask(taskId);
    };
}

// 创建状态标签
function createStatusLabel(status) {
    const statusClass = {
        'success': 'status-success',
        'error': 'status-error',
        'running': 'status-running',
        'timeout': 'status-timeout',
        'pending': 'status-pending',
        'terminated': 'status-timeout' // 使用与超时相同的样式
    }[status] || 'status-pending';
    
    return `<span class="px-2 py-1 rounded-full text-xs ${statusClass}">${getStatusText(status)}</span>`;
}

// 格式化时长
function formatDuration(seconds) {
    if (!seconds && seconds !== 0) return '-';
    
    seconds = parseInt(seconds);
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    let result = '';
    if (hours > 0) result += `${hours}小时`;
    if (minutes > 0) result += `${minutes}分钟`;
    if (remainingSeconds > 0 || result === '') result += `${remainingSeconds}秒`;
    
    return result;
}

// 运行任务
function runTask(taskId) {
    // 检查任务是否禁用
    const task = window.allTasks.find(t => t.id == taskId);
    if (task && task.is_active === 0) {
        showConfirmDialog(`任务"${task.name || `任务 ${taskId}`}"已被禁用，确定要手动执行吗？`).then(confirmed => {
            if (confirmed) {
                executeTask(taskId);
            }
        });
    } else {
        executeTask(taskId);
    }
}

// 执行任务的实际函数
function executeTask(taskId) {
    fetch(`/api/tasks/${taskId}/run`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // 刷新任务列表
            loadTasks();
            
            // 延迟一小段时间，确保后端已经创建了任务日志记录
            setTimeout(() => {
                // 获取最新的日志记录
                fetch(`/api/tasks/${taskId}/logs`)
                    .then(response => response.json())
                    .then(logs => {
                        if (logs && logs.length > 0) {
                            // 找到最新的一条日志（最新执行的任务）
                            const latestLog = logs[0];
                            
                            // 获取任务名称
                            fetch('/api/tasks')
                                .then(response => response.json())
                                .then(tasks => {
                                    const task = tasks.find(t => t.id == taskId);
                                    const taskName = task ? task.name : `任务 ${taskId}`;
                                    
                                    // 存储任务名称用于日志内容显示
                                    window.currentTaskName = taskName;
                                    
                                    // 显示日志内容
                                    showLogContent(latestLog.id, latestLog, true);
                                });
                        }
                    })
                    .catch(error => console.error('获取任务日志失败:', error));
            }, 500);
        } else {
            showNotification('启动任务失败：' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('启动任务失败:', error);
        showNotification('启动任务失败，请检查网络连接', 'error');
    });
}

// 编辑任务
function editTask(taskId) {
    fetch('/api/tasks')
        .then(response => response.json())
        .then(tasks => {
            const task = tasks.find(t => t.id == taskId);
            if (task) {
                document.getElementById('task-modal-title').textContent = '编辑任务';
                document.getElementById('task-id').value = task.id;
                document.getElementById('task-name').value = task.name || '';
                document.getElementById('script-path').value = task.script_path || '';
                document.getElementById('working-dir').value = task.working_dir || '';
                document.getElementById('cron-expression').value = task.cron_expression || '';
                document.getElementById('max-runtime').value = task.max_runtime || 3600;
                document.getElementById('retry-on-error').checked = task.retry_on_error || false;
                document.getElementById('task-remark').value = task.remark || '';
                
                document.getElementById('task-modal').classList.remove('hidden');
            }
        })
        .catch(error => console.error('获取任务信息失败:', error));
}

// 确认对话框
function showConfirmDialog(message, onConfirm) {
    // 创建确认对话框
    const dialog = document.createElement('div');
    dialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[80]';
    dialog.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-sm w-full mx-4">
            <p class="text-gray-800 dark:text-gray-200 mb-6">${message}</p>
            <div class="flex justify-end space-x-3">
                <button class="cancel-btn px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600">取消</button>
                <button class="confirm-btn px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg">确认</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(dialog);
    
    return new Promise((resolve) => {
        dialog.querySelector('.confirm-btn').addEventListener('click', () => {
            document.body.removeChild(dialog);
            resolve(true);
        });
        
        dialog.querySelector('.cancel-btn').addEventListener('click', () => {
            document.body.removeChild(dialog);
            resolve(false);
        });
    });
}

// 启用任务
async function enableTask(taskId) {
    const confirmed = await showConfirmDialog(`确定要启用任务 ${taskId} 吗？`);
    if (confirmed) {
        fetch(`/api/tasks/${taskId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                is_active: true
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('任务已启用', 'success');
                // 重新应用过滤并刷新任务列表
                filterAndRenderTasks();
            } else {
                showNotification('启用任务失败：' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('启用任务失败:', error);
            showNotification('启用任务失败，请检查网络连接', 'error');
        });
    }
}

// 禁用任务
async function disableTask(taskId) {
    const confirmed = await showConfirmDialog(`确定要禁用任务 ${taskId} 吗？`);
    if (confirmed) {
        fetch(`/api/tasks/${taskId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                is_active: false
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('任务已禁用', 'success');
                // 重新应用过滤并刷新任务列表
                filterAndRenderTasks();
            } else {
                showNotification('禁用任务失败：' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('禁用任务失败:', error);
            showNotification('禁用任务失败，请检查网络连接', 'error');
        });
    }
}

// 删除任务
async function deleteTask(taskId) {
    const confirmed = await showConfirmDialog(`确定要删除任务 ${taskId} 吗？此操作不可恢复！`);
    if (confirmed) {
        fetch(`/api/tasks/${taskId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('任务已删除', 'success');
                // 刷新任务列表
                loadTasks();
            } else {
                showNotification('删除任务失败：' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('删除任务失败:', error);
            showNotification('删除任务失败，请检查网络连接', 'error');
        });
    }
}

// 置顶任务
function pinTask(taskId) {
    // 由于后端没有实现置顶功能，这里只显示提示
    showNotification('置顶功能暂未实现', 'info');
}

// 终止任务
function terminateTask(taskId) {
    showConfirmDialog(`确定要终止任务吗？此操作将强制停止任务执行。`).then(confirmed => {
        if (confirmed) {
            fetch(`/api/tasks/${taskId}/terminate`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    
                    // 延迟刷新，确保后端操作完成
                    setTimeout(() => {
                        // 获取任务日志并显示
                        fetch(`/api/tasks/${taskId}/logs`)
                            .then(response => response.json())
                            .then(logs => {
                                if (logs && logs.length > 0) {
                                    const latestLog = logs[0];
                                    
                                    // 获取任务名称
                                    fetch('/api/tasks')
                                        .then(response => response.json())
                                        .then(tasks => {
                                            // 重新加载任务列表和面板数据
                                            loadTasks();
                                            loadDashboardData();
                                            
                                            // 显示日志内容
                                            const task = tasks.find(t => t.id == taskId);
                                            window.currentTaskName = task ? task.name : `任务 ${taskId}`;
                                            showLogContent(latestLog.id, latestLog, true);
                                        });
                                } else {
                                    // 仅刷新任务列表
                                    loadTasks();
                                    loadDashboardData();
                                }
                            })
                            .catch(error => {
                                console.error('获取任务日志失败:', error);
                                // 出现错误时仍然刷新任务列表
                                loadTasks();
                                loadDashboardData();
                            });
                    }, 1000); // 延迟1秒，确保后端完成所有操作
                } else {
                    showNotification('终止任务失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('终止任务失败:', error);
                showNotification('终止任务失败，请检查网络连接', 'error');
            });
        }
    });
}

// 自动刷新任务列表和日志
let autoRefreshInterval = null;

function startAutoRefresh() {
    // 每5秒刷新一次任务列表
    if (!autoRefreshInterval) {
        autoRefreshInterval = setInterval(() => {
            loadTasks();
            loadDashboardData();
            
            // 如果当前显示的是日志内容，则更新日志内容
            const logContentModal = document.getElementById('log-content-modal');
            if (!logContentModal.classList.contains('hidden') && window.currentLogId) {
                fetch(`/api/logs/${window.currentLogId}`)
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('log-content').textContent = data.content;
                        scrollLogToBottom();
                    });
            }
        }, 5000);
    }
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

// 页面加载时启动自动刷新
document.addEventListener('DOMContentLoaded', () => {
    startAutoRefresh();
});

// 页面可见性变化时管理自动刷新
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
        startAutoRefresh();
        loadTasks(); // 立即刷新一次
        loadDashboardData();
    } else {
        stopAutoRefresh();
    }
}); 