---
description: 
globs: 
alwaysApply: false
---
# 任务管理系统 - 开发指南

## 系统架构概述

任务管理系统基于以下架构设计:

1. **核心组件** - Task_manager类处理任务调度、执行和日志记录
2. **Web界面** - Flask应用提供Web界面和REST API
3. **持久化** - SQLite数据库存储任务配置和执行记录
4. **通知系统** - 支持邮件和Webhook通知

## 扩展通知系统

通过扩展 [notification.py](mdc:notification.py) 中的Notification类，可以添加新的通知方式:

1. 在Notification类中添加新的通知方法，例如:
   ```python
   def send_sms_notification(self, phone_numbers, message):
       # 实现短信通知
       pass
   ```

2. 在Task_manager._send_task_notifications方法中添加相应处理逻辑

## 自定义任务执行

在 [task_manager.py](mdc:task_manager.py) 中修改_execute_task方法可以自定义任务执行行为:

1. 支持更多脚本类型（如Shell脚本）:
   ```python
   # 根据脚本扩展名确定执行方式
   if script_path.endswith('.py'):
       cmd = ['python', script_path]
   elif script_path.endswith('.sh'):
       cmd = ['bash', script_path]
   ```

2. 添加环境变量或命令参数支持

## 添加新API端点

在 [app.py](mdc:app.py) 中添加新的路由和功能:

1. 添加数据导出API:
   ```python
   @app.route('/api/export/tasks', methods=['GET'])
   def export_tasks():
       tasks = task_manager.get_tasks(include_inactive=True)
       return jsonify(tasks)
   ```

2. 添加批量操作API:
   ```python
   @app.route('/api/tasks/batch/run', methods=['POST'])
   def batch_run_tasks():
       task_ids = request.json.get('task_ids', [])
       results = {}
       for task_id in task_ids:
           success, message = task_manager.run_task_now(task_id)
           results[task_id] = {'success': success, 'message': message}
       return jsonify(results)
   ```

## 增强前端界面

可以通过修改 [templates/index.html](mdc:templates/index.html) 和添加新的静态文件来改进前端:

1. 添加更多图表和统计信息
2. 实现拖放式任务排序
3. 支持任务分组和标签
4. 添加黑暗模式支持

## 数据库迁移

如需修改数据库结构:

1. 创建迁移脚本添加新表或字段
2. 在Task_manager._init_db方法中更新表结构定义
3. 添加向后兼容的数据迁移逻辑

## 单元测试

建议为核心功能编写测试:

1. 测试任务调度逻辑
2. 测试任务执行和超时处理
3. 测试通知系统
4. 模拟不同错误情况的处理

