# 项目总结文档

## 1. 项目概述

本项目是一个功能强大的任务管理系统，旨在为用户提供一个直观、高效的方式来管理和监控后台任务。系统拥有一个基于Web的现代化用户界面，允许用户动态地添加、编辑、删除、执行和监控任务。所有任务的执行状态和日志都被详细记录，并可通过UI清晰地展示。该系统支持定时任务（通过Cron表达式）和手动触发，并提供了任务执行超时、失败重试等高级功能。

## 2. 技术栈

- **后端**: Python
- **Web框架**: Flask
- **数据库**: SQLite
- **前端**: 
    - HTML
    - TailwindCSS (通过CDN引入)
    - JavaScript (原生)
    - FontAwesome (图标库)

## 3. 目录结构

```
├───app.py              # Flask Web应用入口，提供API接口和Web页面服务
├───main_script.py      # 主要的命令行脚本 (可能用于初始化或测试)
├───notification.py     # 通知模块 (当前为占位符)
├───task_manager.py     # 任务管理核心逻辑，负责任务调度和数据库交互
├───tasks.db            # SQLite 数据库文件
├───requirements.txt    # Python 依赖包
├───templates/
│   └───index.html      # 系统主页的HTML模板
├───static/
│   └───js/
│       └───script.js   # 前端核心JavaScript逻辑
├───test_scripts/       # 用于测试的任务脚本目录
│   └───test_task.py    # 一个示例测试脚本
├───log/                # 日志文件目录
└───GEMINI.md           # 本项目总结文档
```

## 4. 模块与核心功能

### 前端 (`index.html` & `script.js`)

前端是系统的主要交互界面，提供了丰富的功能：

- **响应式UI**: 使用TailwindCSS构建，适配不同尺寸的屏幕。
- **主题切换**: 支持明亮和黑暗两种主题模式，并能根据用户系统偏好自动选择。
- **数据仪表盘**: 
    - 实时展示“执行中”、“已完成”、“失败”的任务数量。
    - 点击数据面板可快速查看对应状态下的任务列表详情。
- **动态任务列表**: 
    - 自动定时刷新（每分钟）任务列表和仪表盘数据。
    - 手动刷新按钮，并带有加载动画。
    - 支持按任务名称、脚本路径、备注进行实时搜索过滤。
    - 可选择是否显示已禁用的任务。
- **任务操作**: 
    - **执行/终止**: 对“等待中”的任务可以手动执行，对“运行中”的任务可以强制终止。
    - **查看日志**: 
        - 查看某个任务的所有历史执行日志列表。
        - 查看单次执行的详细日志内容，对于正在运行的任务，日志会每秒自动刷新。
    - **更多操作 (弹出菜单)**: 
        - **编辑**: 在弹窗中修改任务的各项配置。
        - **禁用/启用**: 快速切换任务的激活状态。
        - **删除**: 从系统中永久删除任务（有确认提示）。
- **弹窗系统**: 
    - **添加/编辑任务**: 通过一个功能完善的表单弹窗来配置任务，包括名称、脚本路径、工作目录、Cron表达式、最大运行时间、是否失败重试等。
    - **日志查看**: 提供独立的弹窗来展示任务的执行日志列表和详细日志内容。
    - **用户体验优化**: 支持使用`ESC`键关闭所有弹窗。
- **通知系统**: 在右上角显示操作结果的通知（如成功、失败、信息），通知会自动消失。

### 后端

#### `app.py` (Flask应用)

作为Web服务器，提供了一系列API端点供前端调用：

- **`/`**: (GET) - 渲染主页面 `index.html`。
- **`/api/dashboard`**: (GET) - 获取仪表盘数据（运行中、已完成、失败的任务数）。
- **`/api/dashboard/<status>`**: (GET) - 获取特定状态（`running`, `completed`, `failed`）下的任务列表。
- **`/api/tasks`**: 
    - (GET) - 获取所有任务的列表。
    - (POST) - 添加一个新任务。
- **`/api/tasks/<task_id>`**: 
    - (PUT) - 更新指定ID的任务信息（包括启用/禁用）。
    - (DELETE) - 删除指定ID的任务。
- **`/api/tasks/<task_id>/run`**: (POST) - 手动执行一个任务。
- **`/api/tasks/<task_id>/terminate`**: (POST) - 终止一个正在运行的任务。
- **`/api/tasks/<task_id>/logs`**: (GET) - 获取指定任务的所有日志记录。
- **`/api/logs/<log_id>`**: (GET) - 获取单条日志的详细内容。

#### `task_manager.py`

这是任务管理的核心，负责与数据库交互和任务的生命周期管理。

- `init_db()`: 初始化数据库，如果`tasks`表不存在则创建它。
- `add_task(...)`: 向数据库中添加一个新任务。
- `update_task(...)`: 更新现有任务的属性。
- `delete_task(task_id)`: 从数据库中删除任务。
- `get_all_tasks()`: 获取所有任务的完整列表。
- `get_task_by_id(task_id)`: 根据ID获取单个任务。
- **任务调度与执行**: (推断) 包含一个调度器（如APScheduler），用于根据任务的Cron表达式定时执行任务。当任务执行时，它会：
    1. 创建一个新的进程来运行指定的脚本。
    2. 记录任务的开始时间，并将状态更新为`running`。
    3. 捕获脚本的输出（stdout/stderr）并实时写入日志文件。
    4. 监控任务的运行时间，如果超过`max_runtime`则终止它。
    5. 任务结束后，更新其状态（`success`或`error`）和结束时间。

#### `notification.py`

- `send_notification(message)`: 一个占位符模块，目前仅将通知消息打印到控制台。未来可以扩展为发送邮件、Webhook等。

#### `main_script.py`

- `main()`: 一个简单的命令行脚本，用于演示如何直接调用`task_manager`和`notification`模块中的功能，可用于快速测试后端逻辑。