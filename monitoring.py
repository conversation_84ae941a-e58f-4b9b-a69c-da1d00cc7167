"""
系统监控和健康检查模块
提供系统状态监控、性能指标收集等功能
"""
import psutil
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from collections import deque


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used: int
    memory_total: int
    disk_percent: float
    disk_used: int
    disk_total: int
    load_average: List[float]
    process_count: int


@dataclass
class TaskMetrics:
    """任务指标数据类"""
    timestamp: datetime
    total_tasks: int
    active_tasks: int
    running_tasks: int
    completed_today: int
    failed_today: int
    avg_execution_time: float
    queue_size: int


class HealthChecker:
    """健康检查器"""
    
    def __init__(self, task_manager):
        """
        初始化健康检查器
        
        参数:
            task_manager: 任务管理器实例
        """
        self.task_manager = task_manager
        self.logger = logging.getLogger(__name__)
        
        # 健康检查阈值
        self.thresholds = {
            'cpu_percent': 90.0,
            'memory_percent': 90.0,
            'disk_percent': 95.0,
            'max_failed_tasks': 10,
            'max_queue_size': 100
        }
        
        # 健康状态
        self.health_status = {
            'overall': 'healthy',
            'components': {
                'system': 'healthy',
                'database': 'healthy',
                'scheduler': 'healthy',
                'tasks': 'healthy'
            },
            'last_check': datetime.now(),
            'issues': []
        }
    
    def check_system_health(self) -> Dict[str, Any]:
        """
        检查系统健康状态
        
        返回:
            系统健康状态字典
        """
        issues = []
        
        try:
            # CPU检查
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > self.thresholds['cpu_percent']:
                issues.append(f"CPU使用率过高: {cpu_percent:.1f}%")
            
            # 内存检查
            memory = psutil.virtual_memory()
            if memory.percent > self.thresholds['memory_percent']:
                issues.append(f"内存使用率过高: {memory.percent:.1f}%")
            
            # 磁盘检查
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            if disk_percent > self.thresholds['disk_percent']:
                issues.append(f"磁盘使用率过高: {disk_percent:.1f}%")
            
            # 进程检查
            try:
                process = psutil.Process()
                if not process.is_running():
                    issues.append("主进程状态异常")
            except psutil.NoSuchProcess:
                issues.append("无法找到主进程")
            
        except Exception as e:
            issues.append(f"系统检查失败: {str(e)}")
        
        return {
            'status': 'healthy' if not issues else 'unhealthy',
            'issues': issues,
            'metrics': {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'disk_percent': disk_percent
            }
        }
    
    def check_database_health(self) -> Dict[str, Any]:
        """
        检查数据库健康状态
        
        返回:
            数据库健康状态字典
        """
        issues = []
        
        try:
            # 测试数据库连接
            tasks = self.task_manager.get_tasks()
            
            # 检查数据库文件大小
            db_path = self.task_manager.db_path
            if hasattr(db_path, 'stat'):
                db_size = db_path.stat().st_size
                # 如果数据库文件超过100MB，发出警告
                if db_size > 100 * 1024 * 1024:
                    issues.append(f"数据库文件过大: {db_size / 1024 / 1024:.1f}MB")
            
        except Exception as e:
            issues.append(f"数据库连接失败: {str(e)}")
        
        return {
            'status': 'healthy' if not issues else 'unhealthy',
            'issues': issues
        }
    
    def check_scheduler_health(self) -> Dict[str, Any]:
        """
        检查调度器健康状态
        
        返回:
            调度器健康状态字典
        """
        issues = []
        
        try:
            # 检查调度器线程是否运行
            if hasattr(self.task_manager, 'scheduler_thread'):
                if not self.task_manager.scheduler_thread.is_alive():
                    issues.append("调度器线程未运行")
            
            # 检查任务队列大小
            if hasattr(self.task_manager, 'get_queue_size'):
                queue_size = self.task_manager.get_queue_size()
                if queue_size > self.thresholds['max_queue_size']:
                    issues.append(f"任务队列过大: {queue_size}")
            
        except Exception as e:
            issues.append(f"调度器检查失败: {str(e)}")
        
        return {
            'status': 'healthy' if not issues else 'unhealthy',
            'issues': issues
        }
    
    def check_tasks_health(self) -> Dict[str, Any]:
        """
        检查任务健康状态
        
        返回:
            任务健康状态字典
        """
        issues = []
        
        try:
            # 获取今天的失败任务数
            today = datetime.now().date()
            logs = self.task_manager.get_task_logs(limit=1000)
            
            failed_today = sum(
                1 for log in logs
                if (log['status'] in ['error', 'timeout'] and
                    datetime.strptime(log['start_time'], '%Y-%m-%d %H:%M:%S').date() == today)
            )
            
            if failed_today > self.thresholds['max_failed_tasks']:
                issues.append(f"今日失败任务过多: {failed_today}")
            
            # 检查长时间运行的任务
            running_tasks = self.task_manager.running_tasks
            current_time = datetime.now()
            
            for task_id, task_info in running_tasks.items():
                if 'start_time' in task_info:
                    start_time = task_info['start_time']
                    if isinstance(start_time, str):
                        start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
                    
                    running_time = (current_time - start_time).total_seconds()
                    # 如果任务运行超过2小时，发出警告
                    if running_time > 7200:
                        issues.append(f"任务 {task_id} 运行时间过长: {running_time/3600:.1f}小时")
            
        except Exception as e:
            issues.append(f"任务检查失败: {str(e)}")
        
        return {
            'status': 'healthy' if not issues else 'unhealthy',
            'issues': issues,
            'metrics': {
                'failed_today': failed_today,
                'running_tasks': len(running_tasks)
            }
        }
    
    def perform_health_check(self) -> Dict[str, Any]:
        """
        执行完整的健康检查
        
        返回:
            完整的健康状态报告
        """
        self.logger.info("开始执行健康检查")
        
        # 检查各个组件
        system_health = self.check_system_health()
        database_health = self.check_database_health()
        scheduler_health = self.check_scheduler_health()
        tasks_health = self.check_tasks_health()
        
        # 汇总健康状态
        all_issues = (
            system_health['issues'] +
            database_health['issues'] +
            scheduler_health['issues'] +
            tasks_health['issues']
        )
        
        overall_status = 'healthy' if not all_issues else 'unhealthy'
        
        # 更新健康状态
        self.health_status = {
            'overall': overall_status,
            'components': {
                'system': system_health['status'],
                'database': database_health['status'],
                'scheduler': scheduler_health['status'],
                'tasks': tasks_health['status']
            },
            'last_check': datetime.now(),
            'issues': all_issues,
            'details': {
                'system': system_health,
                'database': database_health,
                'scheduler': scheduler_health,
                'tasks': tasks_health
            }
        }
        
        self.logger.info(f"健康检查完成，状态: {overall_status}")
        return self.health_status


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, task_manager, collection_interval: int = 60, max_history: int = 1440):
        """
        初始化系统监控器
        
        参数:
            task_manager: 任务管理器实例
            collection_interval: 数据收集间隔（秒）
            max_history: 最大历史记录数（默认24小时）
        """
        self.task_manager = task_manager
        self.collection_interval = collection_interval
        self.max_history = max_history
        
        # 历史数据存储
        self.system_metrics_history = deque(maxlen=max_history)
        self.task_metrics_history = deque(maxlen=max_history)
        
        # 线程控制
        self._stop_event = threading.Event()
        self._monitor_thread: Optional[threading.Thread] = None
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        # 健康检查器
        self.health_checker = HealthChecker(task_manager)
    
    def start_monitoring(self):
        """启动监控"""
        if self._monitor_thread and self._monitor_thread.is_alive():
            self.logger.warning("监控已经在运行中")
            return
        
        self.logger.info("启动系统监控")
        self._stop_event.clear()
        self._monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self._monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.logger.info("停止系统监控")
        self._stop_event.set()
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
    
    def collect_system_metrics(self) -> SystemMetrics:
        """
        收集系统指标
        
        返回:
            系统指标对象
        """
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存信息
            memory = psutil.virtual_memory()
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 负载平均值
            load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
            
            # 进程数
            process_count = len(psutil.pids())
            
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used=memory.used,
                memory_total=memory.total,
                disk_percent=disk_percent,
                disk_used=disk.used,
                disk_total=disk.total,
                load_average=list(load_avg),
                process_count=process_count
            )
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            return None
    
    def collect_task_metrics(self) -> TaskMetrics:
        """
        收集任务指标
        
        返回:
            任务指标对象
        """
        try:
            # 获取任务统计
            all_tasks = self.task_manager.get_tasks(include_inactive=True)
            active_tasks = [task for task in all_tasks if task.get('is_active', True)]
            running_tasks = self.task_manager.running_tasks
            
            # 获取今天的任务执行情况
            today = datetime.now().date()
            logs = self.task_manager.get_task_logs(limit=1000)
            
            completed_today = sum(
                1 for log in logs
                if (log['status'] == 'success' and
                    datetime.strptime(log['start_time'], '%Y-%m-%d %H:%M:%S').date() == today)
            )
            
            failed_today = sum(
                1 for log in logs
                if (log['status'] in ['error', 'timeout'] and
                    datetime.strptime(log['start_time'], '%Y-%m-%d %H:%M:%S').date() == today)
            )
            
            # 计算平均执行时间
            completed_logs = [log for log in logs if log['status'] == 'success' and log['duration']]
            avg_execution_time = (
                sum(log['duration'] for log in completed_logs) / len(completed_logs)
                if completed_logs else 0
            )
            
            return TaskMetrics(
                timestamp=datetime.now(),
                total_tasks=len(all_tasks),
                active_tasks=len(active_tasks),
                running_tasks=len(running_tasks),
                completed_today=completed_today,
                failed_today=failed_today,
                avg_execution_time=avg_execution_time,
                queue_size=0  # 需要从调度器获取
            )
            
        except Exception as e:
            self.logger.error(f"收集任务指标失败: {e}")
            return None
    
    def get_current_status(self) -> Dict[str, Any]:
        """
        获取当前系统状态
        
        返回:
            当前系统状态字典
        """
        system_metrics = self.collect_system_metrics()
        task_metrics = self.collect_task_metrics()
        health_status = self.health_checker.perform_health_check()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'system_metrics': asdict(system_metrics) if system_metrics else None,
            'task_metrics': asdict(task_metrics) if task_metrics else None,
            'health_status': health_status
        }
    
    def get_metrics_history(self, hours: int = 24) -> Dict[str, Any]:
        """
        获取指定时间范围内的指标历史
        
        参数:
            hours: 小时数
            
        返回:
            历史指标数据
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 过滤历史数据
        system_history = [
            asdict(metrics) for metrics in self.system_metrics_history
            if metrics.timestamp >= cutoff_time
        ]
        
        task_history = [
            asdict(metrics) for metrics in self.task_metrics_history
            if metrics.timestamp >= cutoff_time
        ]
        
        return {
            'system_metrics': system_history,
            'task_metrics': task_history,
            'time_range': {
                'start': cutoff_time.isoformat(),
                'end': datetime.now().isoformat(),
                'hours': hours
            }
        }
    
    def _monitoring_loop(self):
        """监控循环"""
        self.logger.info("监控循环开始")
        
        while not self._stop_event.is_set():
            try:
                # 收集系统指标
                system_metrics = self.collect_system_metrics()
                if system_metrics:
                    self.system_metrics_history.append(system_metrics)
                
                # 收集任务指标
                task_metrics = self.collect_task_metrics()
                if task_metrics:
                    self.task_metrics_history.append(task_metrics)
                
                # 等待下次收集
                self._stop_event.wait(timeout=self.collection_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                self._stop_event.wait(timeout=60)
        
        self.logger.info("监控循环结束")
